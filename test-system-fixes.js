/**
 * @file test-system-fixes.js
 * @description Chrome扩展系统性修复验证测试脚本
 * @version 1.0.0
 * @date 2024-12-19
 */

// 测试配置
const TEST_CONFIG = {
  timeout: 5000, // 5秒超时
  retryCount: 3,
  verbose: true
};

/**
 * 测试结果收集器
 */
class TestResultCollector {
  constructor() {
    this.results = [];
    this.startTime = Date.now();
  }

  addResult(testName, success, message, details = null) {
    this.results.push({
      testName,
      success,
      message,
      details,
      timestamp: Date.now()
    });
    
    if (TEST_CONFIG.verbose) {
      const status = success ? '✅' : '❌';
      console.log(`${status} ${testName}: ${message}`);
      if (details) {
        console.log(`   详情: ${JSON.stringify(details, null, 2)}`);
      }
    }
  }

  generateReport() {
    const endTime = Date.now();
    const duration = endTime - this.startTime;
    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;

    const report = {
      summary: {
        total: totalTests,
        passed: passedTests,
        failed: failedTests,
        successRate: totalTests > 0 ? (passedTests / totalTests * 100).toFixed(2) + '%' : '0%',
        duration: duration + 'ms'
      },
      results: this.results
    };

    console.log('\n🔍 系统修复验证报告');
    console.log('='.repeat(50));
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过: ${passedTests}`);
    console.log(`失败: ${failedTests}`);
    console.log(`成功率: ${report.summary.successRate}`);
    console.log(`耗时: ${report.summary.duration}`);
    console.log('='.repeat(50));

    return report;
  }
}

/**
 * 阶段1测试：模块兼容性验证
 */
async function testModuleCompatibility(collector) {
  console.log('\n📦 阶段1：模块兼容性测试');
  
  // 测试1.1：MESSAGE_TYPES全局可用性
  try {
    const hasWindowMessageTypes = typeof window !== 'undefined' && window.MESSAGE_TYPES;
    const hasGlobalMessageTypes = typeof globalThis !== 'undefined' && globalThis.MESSAGE_TYPES;
    
    if (hasWindowMessageTypes || hasGlobalMessageTypes) {
      collector.addResult(
        '模块兼容性-MESSAGE_TYPES可用性',
        true,
        'MESSAGE_TYPES在全局环境中可用',
        { window: !!hasWindowMessageTypes, globalThis: !!hasGlobalMessageTypes }
      );
    } else {
      collector.addResult(
        '模块兼容性-MESSAGE_TYPES可用性',
        false,
        'MESSAGE_TYPES在全局环境中不可用'
      );
    }
  } catch (error) {
    collector.addResult(
      '模块兼容性-MESSAGE_TYPES可用性',
      false,
      '测试MESSAGE_TYPES可用性时出错: ' + error.message
    );
  }

  // 测试1.2：消息类型完整性
  try {
    const messageTypes = window.MESSAGE_TYPES || globalThis.MESSAGE_TYPES || {};
    const requiredTypes = [
      'CONTENT_CAPTURED', 'CHAT_SEND', 'ANALYSIS_REQUEST',
      'SETTINGS_GET', 'NOTION_CONNECT', 'CURSOR_ENHANCE_TOGGLE'
    ];
    
    const missingTypes = requiredTypes.filter(type => !messageTypes[type]);
    
    if (missingTypes.length === 0) {
      collector.addResult(
        '模块兼容性-消息类型完整性',
        true,
        '所有必需的消息类型都已定义',
        { totalTypes: Object.keys(messageTypes).length }
      );
    } else {
      collector.addResult(
        '模块兼容性-消息类型完整性',
        false,
        '缺少必需的消息类型',
        { missing: missingTypes }
      );
    }
  } catch (error) {
    collector.addResult(
      '模块兼容性-消息类型完整性',
      false,
      '测试消息类型完整性时出错: ' + error.message
    );
  }
}

/**
 * 阶段2测试：后台服务消息处理验证
 */
async function testBackgroundMessageHandling(collector) {
  console.log('\n🔧 阶段2：后台服务消息处理测试');
  
  // 测试2.1：Chrome Runtime API可用性
  try {
    const hasRuntimeAPI = typeof chrome !== 'undefined' && chrome.runtime;
    
    if (hasRuntimeAPI) {
      collector.addResult(
        '后台服务-Chrome Runtime API',
        true,
        'Chrome Runtime API可用'
      );
    } else {
      collector.addResult(
        '后台服务-Chrome Runtime API',
        false,
        'Chrome Runtime API不可用（可能在非扩展环境中运行）'
      );
    }
  } catch (error) {
    collector.addResult(
      '后台服务-Chrome Runtime API',
      false,
      '测试Chrome Runtime API时出错: ' + error.message
    );
  }

  // 测试2.2：消息发送功能
  if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
    try {
      // 发送测试消息
      const testMessage = {
        type: 'ai:test:notification',
        payload: { test: true },
        requestId: 'test-' + Date.now()
      };

      const response = await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('消息发送超时'));
        }, TEST_CONFIG.timeout);

        chrome.runtime.sendMessage(testMessage, (response) => {
          clearTimeout(timeout);
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(response);
          }
        });
      });

      if (response && typeof response === 'object') {
        collector.addResult(
          '后台服务-消息发送功能',
          true,
          '消息发送和接收正常',
          { responseType: typeof response, hasSuccess: 'success' in response }
        );
      } else {
        collector.addResult(
          '后台服务-消息发送功能',
          false,
          '消息发送成功但响应格式异常',
          { response }
        );
      }
    } catch (error) {
      collector.addResult(
        '后台服务-消息发送功能',
        false,
        '消息发送失败: ' + error.message
      );
    }
  } else {
    collector.addResult(
      '后台服务-消息发送功能',
      false,
      '无法测试消息发送功能（Chrome API不可用）'
    );
  }
}

/**
 * 阶段3测试：UI组件方法验证
 */
async function testUIComponentMethods(collector) {
  console.log('\n🎨 阶段3：UI组件方法测试');
  
  // 测试3.1：AI侧边栏面板实例
  try {
    const hasSidebarPanel = typeof window !== 'undefined' && window.aiSidebarPanel;
    
    if (hasSidebarPanel) {
      collector.addResult(
        'UI组件-侧边栏面板实例',
        true,
        'AI侧边栏面板实例存在'
      );
    } else {
      collector.addResult(
        'UI组件-侧边栏面板实例',
        false,
        'AI侧边栏面板实例不存在'
      );
    }
  } catch (error) {
    collector.addResult(
      'UI组件-侧边栏面板实例',
      false,
      '测试侧边栏面板实例时出错: ' + error.message
    );
  }

  // 测试3.2：关键方法存在性
  if (typeof window !== 'undefined' && window.aiSidebarPanel) {
    try {
      const panel = window.aiSidebarPanel;
      const requiredMethods = [
        'loadCurrentSettings', 'getCurrentPageInfo', 'getAnalysisOptions',
        'loadTemplates', 'loadCursorEnhancementStatus', 'loadComparablePages'
      ];
      
      const missingMethods = requiredMethods.filter(method => typeof panel[method] !== 'function');
      
      if (missingMethods.length === 0) {
        collector.addResult(
          'UI组件-关键方法存在性',
          true,
          '所有关键方法都已实现',
          { methodCount: requiredMethods.length }
        );
      } else {
        collector.addResult(
          'UI组件-关键方法存在性',
          false,
          '缺少关键方法实现',
          { missing: missingMethods }
        );
      }
    } catch (error) {
      collector.addResult(
        'UI组件-关键方法存在性',
        false,
        '测试关键方法存在性时出错: ' + error.message
      );
    }
  }
}

/**
 * 阶段4测试：CSS布局验证
 */
async function testCSSLayout(collector) {
  console.log('\n🎨 阶段4：CSS布局测试');
  
  // 测试4.1：侧边栏容器存在性
  try {
    const sidebarElement = document.querySelector('.ai-sidebar');
    
    if (sidebarElement) {
      collector.addResult(
        'CSS布局-侧边栏容器',
        true,
        '侧边栏容器元素存在'
      );
    } else {
      collector.addResult(
        'CSS布局-侧边栏容器',
        false,
        '侧边栏容器元素不存在'
      );
    }
  } catch (error) {
    collector.addResult(
      'CSS布局-侧边栏容器',
      false,
      '测试侧边栏容器时出错: ' + error.message
    );
  }

  // 测试4.2：滚动区域样式
  try {
    const scrollableElements = document.querySelectorAll('.ai-chat__messages, .ai-analysis__results, .ai-settings-content');
    let validScrollElements = 0;
    
    scrollableElements.forEach(element => {
      const styles = window.getComputedStyle(element);
      if (styles.overflowY === 'auto' || styles.overflowY === 'scroll') {
        validScrollElements++;
      }
    });
    
    if (validScrollElements > 0) {
      collector.addResult(
        'CSS布局-滚动区域样式',
        true,
        '滚动区域样式配置正确',
        { validElements: validScrollElements, totalElements: scrollableElements.length }
      );
    } else {
      collector.addResult(
        'CSS布局-滚动区域样式',
        false,
        '滚动区域样式配置异常'
      );
    }
  } catch (error) {
    collector.addResult(
      'CSS布局-滚动区域样式',
      false,
      '测试滚动区域样式时出错: ' + error.message
    );
  }
}

/**
 * 主测试函数
 */
async function runSystemFixesTest() {
  console.log('🚀 开始Chrome扩展系统修复验证测试');
  console.log('测试时间:', new Date().toLocaleString());
  
  const collector = new TestResultCollector();
  
  try {
    // 运行所有测试阶段
    await testModuleCompatibility(collector);
    await testBackgroundMessageHandling(collector);
    await testUIComponentMethods(collector);
    await testCSSLayout(collector);
    
    // 生成最终报告
    const report = collector.generateReport();
    
    // 保存测试结果到localStorage（如果可用）
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem('ai-sidebar-test-results', JSON.stringify(report));
      console.log('\n💾 测试结果已保存到localStorage');
    }
    
    return report;
    
  } catch (error) {
    console.error('❌ 测试执行过程中出现错误:', error);
    collector.addResult(
      '系统测试-执行错误',
      false,
      '测试执行过程中出现错误: ' + error.message
    );
    
    return collector.generateReport();
  }
}

// 导出测试函数
if (typeof window !== 'undefined') {
  window.runSystemFixesTest = runSystemFixesTest;
  window.TestResultCollector = TestResultCollector;
}

// 如果在扩展环境中，自动运行测试
if (typeof chrome !== 'undefined' && chrome.runtime) {
  // 延迟执行，确保所有模块都已加载
  setTimeout(() => {
    runSystemFixesTest().then(report => {
      console.log('🎉 系统修复验证测试完成');
    }).catch(error => {
      console.error('❌ 系统修复验证测试失败:', error);
    });
  }, 2000);
}
