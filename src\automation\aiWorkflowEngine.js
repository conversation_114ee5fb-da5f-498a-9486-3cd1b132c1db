/**
 * @file AI工作流自动化引擎
 * @description 提供可视化工作流构建、触发器管理和自动化执行功能
 */

/**
 * @class AiWorkflowEngine
 * @description AI工作流引擎，支持创建、管理和执行自动化工作流
 */
class AiWorkflowEngine {
  /**
   * @function constructor - 构造函数
   * @description 初始化工作流引擎
   * @param {Object} apiManager - API管理器实例
   */
  constructor(apiManager) {
    this.apiManager = apiManager;

    // 工作流配置
    this.config = {
      // 执行配置
      maxConcurrentWorkflows: 5,
      defaultTimeout: 30000, // 30秒
      retryAttempts: 3,
      retryDelay: 1000, // 1秒

      // 调度配置
      schedulerInterval: 60000, // 1分钟
      maxScheduledWorkflows: 50,

      // 存储配置
      storageKey: 'ai_workflows',
      maxWorkflowHistory: 100
    };

    // 工作流状态
    this.workflows = new Map();
    this.activeExecutions = new Map();
    this.scheduledWorkflows = new Map();
    this.workflowHistory = [];

    // 触发器管理
    this.triggers = new Map();
    this.activeTriggers = new Set();

    // 节点类型定义
    this.nodeTypes = new Map([
      ['trigger', {
        name: '触发器',
        category: 'input',
        icon: '🚀',
        inputs: [],
        outputs: ['output'],
        configurable: ['triggerType', 'conditions']
      }],
      ['action', {
        name: '动作',
        category: 'process',
        icon: '⚡',
        inputs: ['input'],
        outputs: ['output'],
        configurable: ['actionType', 'parameters']
      }],
      ['condition', {
        name: '条件',
        category: 'logic',
        icon: '🔀',
        inputs: ['input'],
        outputs: ['true', 'false'],
        configurable: ['conditionType', 'expression']
      }],
      ['delay', {
        name: '延迟',
        category: 'utility',
        icon: '⏱️',
        inputs: ['input'],
        outputs: ['output'],
        configurable: ['duration']
      }],
      ['transform', {
        name: '转换',
        category: 'process',
        icon: '🔄',
        inputs: ['input'],
        outputs: ['output'],
        configurable: ['transformType', 'mapping']
      }],
      ['output', {
        name: '输出',
        category: 'output',
        icon: '📤',
        inputs: ['input'],
        outputs: [],
        configurable: ['outputType', 'destination']
      }]
    ]);

    // 预定义工作流模板
    this.templates = new Map([
      ['content_review', {
        name: '内容审查工作流',
        description: '自动审查页面内容并生成报告',
        nodes: [
          { type: 'trigger', config: { triggerType: 'page_load' } },
          { type: 'action', config: { actionType: 'extract_content' } },
          { type: 'action', config: { actionType: 'analyze_content' } },
          { type: 'condition', config: { conditionType: 'content_quality' } },
          { type: 'output', config: { outputType: 'notification' } }
        ]
      }],
      ['data_extraction', {
        name: '数据提取工作流',
        description: '从网页提取结构化数据',
        nodes: [
          { type: 'trigger', config: { triggerType: 'manual' } },
          { type: 'action', config: { actionType: 'extract_data' } },
          { type: 'transform', config: { transformType: 'format_data' } },
          { type: 'output', config: { outputType: 'export_csv' } }
        ]
      }],
      ['auto_summarize', {
        name: '自动摘要工作流',
        description: '自动生成页面内容摘要',
        nodes: [
          { type: 'trigger', config: { triggerType: 'content_change' } },
          { type: 'action', config: { actionType: 'summarize_content' } },
          { type: 'output', config: { outputType: 'save_summary' } }
        ]
      }]
    ]);

    // 统计信息
    this.stats = {
      totalWorkflows: 0,
      executedWorkflows: 0,
      successfulExecutions: 0,
      failedExecutions: 0,
      averageExecutionTime: 0,
      totalExecutionTime: 0
    };

    // 初始化
    this.init();
  }

  /**
   * @function init - 初始化工作流引擎
   * @description 初始化引擎并加载已保存的工作流
   */
  async init() {
    console.log('[工作流] 初始化AI工作流引擎...');

    // 加载已保存的工作流
    await this.loadWorkflows();

    // 启动调度器
    this.startScheduler();

    // 注册默认触发器
    this.registerDefaultTriggers();

    console.log('[工作流] AI工作流引擎初始化完成');
  }

  /**
   * @function createWorkflow - 创建工作流
   * @description 创建新的工作流
   * @param {Object} workflowData - 工作流数据
   * @returns {string} 工作流ID
   */
  createWorkflow(workflowData) {
    const {
      name,
      description = '',
      nodes = [],
      connections = [],
      triggers = [],
      settings = {}
    } = workflowData;

    const workflowId = this.generateWorkflowId();

    const workflow = {
      id: workflowId,
      name,
      description,
      nodes: this.processNodes(nodes),
      connections: this.processConnections(connections),
      triggers: triggers,
      settings: {
        enabled: true,
        timeout: this.config.defaultTimeout,
        retryAttempts: this.config.retryAttempts,
        ...settings
      },
      metadata: {
        created: Date.now(),
        modified: Date.now(),
        version: 1,
        executionCount: 0,
        lastExecution: null
      }
    };

    // 验证工作流
    const validation = this.validateWorkflow(workflow);
    if (!validation.valid) {
      throw new Error(`工作流验证失败: ${validation.errors.join(', ')}`);
    }

    // 保存工作流
    this.workflows.set(workflowId, workflow);
    this.saveWorkflows();

    // 注册触发器
    this.registerWorkflowTriggers(workflow);

    this.stats.totalWorkflows++;

    console.log(`[工作流] ✅ 工作流已创建: ${name} (${workflowId})`);

    return workflowId;
  }

  /**
   * @function executeWorkflow - 执行工作流
   * @description 执行指定的工作流
   * @param {string} workflowId - 工作流ID
   * @param {Object} context - 执行上下文
   * @returns {Promise<Object>} 执行结果
   */
  async executeWorkflow(workflowId, context = {}) {
    const workflow = this.workflows.get(workflowId);
    if (!workflow) {
      throw new Error(`工作流不存在: ${workflowId}`);
    }

    if (!workflow.settings.enabled) {
      throw new Error(`工作流已禁用: ${workflowId}`);
    }

    const executionId = this.generateExecutionId();
    const startTime = Date.now();

    console.log(`[工作流] 🚀 开始执行工作流: ${workflow.name} (${executionId})`);

    const execution = {
      id: executionId,
      workflowId: workflowId,
      status: 'running',
      startTime: startTime,
      context: { ...context },
      results: new Map(),
      errors: [],
      currentNode: null
    };

    this.activeExecutions.set(executionId, execution);

    try {
      // 执行工作流
      const result = await this.runWorkflowExecution(execution, workflow);

      // 更新执行状态
      execution.status = 'completed';
      execution.endTime = Date.now();
      execution.duration = execution.endTime - execution.startTime;
      execution.result = result;

      // 更新统计
      this.updateExecutionStats(execution, true);

      // 更新工作流元数据
      workflow.metadata.executionCount++;
      workflow.metadata.lastExecution = execution.endTime;

      console.log(`[工作流] ✅ 工作流执行完成: ${workflow.name} (${execution.duration}ms)`);

      return {
        success: true,
        executionId: executionId,
        result: result,
        duration: execution.duration
      };

    } catch (error) {
      // 更新执行状态
      execution.status = 'failed';
      execution.endTime = Date.now();
      execution.duration = execution.endTime - execution.startTime;
      execution.error = error.message;

      // 更新统计
      this.updateExecutionStats(execution, false);

      console.error(`[工作流] ❌ 工作流执行失败: ${workflow.name}`, error);

      return {
        success: false,
        executionId: executionId,
        error: error.message,
        duration: execution.duration
      };

    } finally {
      // 清理活跃执行
      this.activeExecutions.delete(executionId);

      // 保存执行历史
      this.saveExecutionHistory(execution);
    }
  }

  /**
   * @function runWorkflowExecution - 运行工作流执行
   * @description 执行工作流的具体逻辑
   * @param {Object} execution - 执行对象
   * @param {Object} workflow - 工作流对象
   * @returns {Promise<Object>} 执行结果
   */
  async runWorkflowExecution(execution, workflow) {
    const { nodes, connections } = workflow;

    // 找到起始节点（触发器节点）
    const startNodes = nodes.filter(node => node.type === 'trigger');
    if (startNodes.length === 0) {
      throw new Error('工作流没有触发器节点');
    }

    // 执行图遍历
    const executionQueue = [...startNodes];
    const executedNodes = new Set();
    const results = new Map();

    while (executionQueue.length > 0) {
      const currentNode = executionQueue.shift();

      if (executedNodes.has(currentNode.id)) {
        continue;
      }

      execution.currentNode = currentNode.id;

      try {
        // 执行节点
        const nodeResult = await this.executeNode(currentNode, execution, results);
        results.set(currentNode.id, nodeResult);
        executedNodes.add(currentNode.id);

        // 根据结果决定下一步
        const nextNodes = this.getNextNodes(currentNode, nodeResult, connections, nodes);
        executionQueue.push(...nextNodes);

      } catch (error) {
        execution.errors.push({
          nodeId: currentNode.id,
          error: error.message,
          timestamp: Date.now()
        });

        // 根据错误处理策略决定是否继续
        if (workflow.settings.stopOnError !== false) {
          throw error;
        }
      }
    }

    return {
      executedNodes: Array.from(executedNodes),
      results: Object.fromEntries(results),
      errors: execution.errors
    };
  }

  /**
   * @function executeNode - 执行节点
   * @description 执行单个工作流节点
   * @param {Object} node - 节点对象
   * @param {Object} execution - 执行上下文
   * @param {Map} results - 之前的结果
   * @returns {Promise<Object>} 节点执行结果
   */
  async executeNode(node, execution, results) {
    console.log(`[工作流] 🔄 执行节点: ${node.type} (${node.id})`);

    const startTime = Date.now();

    try {
      let result;

      switch (node.type) {
        case 'trigger':
          result = await this.executeTriggerNode(node, execution);
          break;

        case 'action':
          result = await this.executeActionNode(node, execution, results);
          break;

        case 'condition':
          result = await this.executeConditionNode(node, execution, results);
          break;

        case 'delay':
          result = await this.executeDelayNode(node, execution);
          break;

        case 'transform':
          result = await this.executeTransformNode(node, execution, results);
          break;

        case 'output':
          result = await this.executeOutputNode(node, execution, results);
          break;

        default:
          throw new Error(`未知节点类型: ${node.type}`);
      }

      const duration = Date.now() - startTime;
      console.log(`[工作流] ✅ 节点执行完成: ${node.type} (${duration}ms)`);

      return {
        success: true,
        data: result,
        duration: duration,
        timestamp: Date.now()
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`[工作流] ❌ 节点执行失败: ${node.type}`, error);

      throw new Error(`节点执行失败 (${node.type}): ${error.message}`);
    }
  }

  /**
   * @function executeTriggerNode - 执行触发器节点
   * @description 执行触发器类型的节点
   * @param {Object} node - 触发器节点
   * @param {Object} execution - 执行上下文
   * @returns {Promise<Object>} 执行结果
   */
  async executeTriggerNode(node, execution) {
    const { triggerType, conditions } = node.config;

    // 触发器节点通常只是传递初始数据
    return {
      triggerType: triggerType,
      conditions: conditions,
      context: execution.context,
      timestamp: Date.now()
    };
  }

  /**
   * @function executeActionNode - 执行动作节点
   * @description 执行动作类型的节点
   * @param {Object} node - 动作节点
   * @param {Object} execution - 执行上下文
   * @param {Map} results - 之前的结果
   * @returns {Promise<Object>} 执行结果
   */
  async executeActionNode(node, execution, results) {
    const { actionType, parameters } = node.config;

    switch (actionType) {
      case 'extract_content':
        return await this.extractPageContent();

      case 'analyze_content':
        const content = this.getInputData(node, results);
        return await this.analyzeContent(content);

      case 'summarize_content':
        const textContent = this.getInputData(node, results);
        return await this.summarizeContent(textContent);

      case 'extract_data':
        return await this.extractStructuredData(parameters);

      case 'send_notification':
        const message = this.getInputData(node, results);
        return await this.sendNotification(message, parameters);

      default:
        throw new Error(`未知动作类型: ${actionType}`);
    }
  }

  /**
   * @function executeConditionNode - 执行条件节点
   * @description 执行条件判断节点
   * @param {Object} node - 条件节点
   * @param {Object} execution - 执行上下文
   * @param {Map} results - 之前的结果
   * @returns {Promise<Object>} 执行结果
   */
  async executeConditionNode(node, execution, results) {
    const { conditionType, expression } = node.config;
    const inputData = this.getInputData(node, results);

    let conditionResult = false;

    switch (conditionType) {
      case 'content_quality':
        conditionResult = await this.evaluateContentQuality(inputData);
        break;

      case 'data_exists':
        conditionResult = inputData && Object.keys(inputData).length > 0;
        break;

      case 'custom_expression':
        conditionResult = this.evaluateExpression(expression, inputData);
        break;

      default:
        throw new Error(`未知条件类型: ${conditionType}`);
    }

    return {
      condition: conditionResult,
      branch: conditionResult ? 'true' : 'false',
      inputData: inputData
    };
  }

  /**
   * @function executeDelayNode - 执行延迟节点
   * @description 执行延迟类型的节点
   * @param {Object} node - 延迟节点
   * @param {Object} execution - 执行上下文
   * @returns {Promise<Object>} 执行结果
   */
  async executeDelayNode(node, execution) {
    const { duration } = node.config;
    const delayMs = parseInt(duration) || 1000;

    await new Promise(resolve => setTimeout(resolve, delayMs));

    return {
      delayed: delayMs,
      timestamp: Date.now()
    };
  }

  /**
   * @function executeTransformNode - 执行转换节点
   * @description 执行数据转换节点
   * @param {Object} node - 转换节点
   * @param {Object} execution - 执行上下文
   * @param {Map} results - 之前的结果
   * @returns {Promise<Object>} 执行结果
   */
  async executeTransformNode(node, execution, results) {
    const { transformType, mapping } = node.config;
    const inputData = this.getInputData(node, results);

    switch (transformType) {
      case 'format_data':
        return this.formatData(inputData, mapping);

      case 'filter_data':
        return this.filterData(inputData, mapping);

      case 'aggregate_data':
        return this.aggregateData(inputData, mapping);

      default:
        throw new Error(`未知转换类型: ${transformType}`);
    }
  }

  /**
   * @function executeOutputNode - 执行输出节点
   * @description 执行输出类型的节点
   * @param {Object} node - 输出节点
   * @param {Object} execution - 执行上下文
   * @param {Map} results - 之前的结果
   * @returns {Promise<Object>} 执行结果
   */
  async executeOutputNode(node, execution, results) {
    const { outputType, destination } = node.config;
    const inputData = this.getInputData(node, results);

    switch (outputType) {
      case 'notification':
        return await this.sendNotification(inputData, destination);

      case 'save_summary':
        return await this.saveSummary(inputData, destination);

      case 'export_csv':
        return await this.exportToCsv(inputData, destination);

      case 'webhook':
        return await this.sendWebhook(inputData, destination);

      default:
        throw new Error(`未知输出类型: ${outputType}`);
    }
  }

  /**
   * @function getInputData - 获取输入数据
   * @description 从前置节点获取输入数据
   * @param {Object} node - 当前节点
   * @param {Map} results - 执行结果
   * @returns {Object} 输入数据
   */
  getInputData(node, results) {
    // 简化实现：获取最近的结果作为输入
    const resultEntries = Array.from(results.entries());
    if (resultEntries.length > 0) {
      const lastResult = resultEntries[resultEntries.length - 1][1];
      return lastResult.data;
    }
    return {};
  }

  /**
   * @function getNextNodes - 获取下一个节点
   * @description 根据当前节点结果获取下一个要执行的节点
   * @param {Object} currentNode - 当前节点
   * @param {Object} nodeResult - 节点执行结果
   * @param {Array} connections - 连接配置
   * @param {Array} nodes - 所有节点
   * @returns {Array} 下一个节点数组
   */
  getNextNodes(currentNode, nodeResult, connections, nodes) {
    const nextNodes = [];

    // 查找从当前节点出发的连接
    const outgoingConnections = connections.filter(conn =>
      conn.source === currentNode.id
    );

    for (const connection of outgoingConnections) {
      // 检查连接条件
      if (this.shouldFollowConnection(connection, nodeResult)) {
        const nextNode = nodes.find(node => node.id === connection.target);
        if (nextNode) {
          nextNodes.push(nextNode);
        }
      }
    }

    return nextNodes;
  }

  /**
   * @function shouldFollowConnection - 检查是否应该跟随连接
   * @description 检查是否应该跟随特定的连接
   * @param {Object} connection - 连接对象
   * @param {Object} nodeResult - 节点结果
   * @returns {boolean} 是否跟随连接
   */
  shouldFollowConnection(connection, nodeResult) {
    // 如果没有条件，总是跟随
    if (!connection.condition) {
      return true;
    }

    // 检查条件节点的分支
    if (nodeResult.data && nodeResult.data.branch) {
      return connection.condition === nodeResult.data.branch;
    }

    return true;
  }

  /**
   * @function extractPageContent - 提取页面内容
   * @description 提取当前页面的内容
   * @returns {Promise<Object>} 页面内容
   */
  async extractPageContent() {
    try {
      const content = {
        title: document.title,
        url: window.location.href,
        text: document.body.textContent.trim(),
        html: document.documentElement.outerHTML,
        metadata: {
          description: document.querySelector('meta[name="description"]')?.content || '',
          keywords: document.querySelector('meta[name="keywords"]')?.content || '',
          author: document.querySelector('meta[name="author"]')?.content || ''
        }
      };

      return content;
    } catch (error) {
      throw new Error(`页面内容提取失败: ${error.message}`);
    }
  }

  /**
   * @function analyzeContent - 分析内容
   * @description 使用AI分析内容
   * @param {Object} content - 要分析的内容
   * @returns {Promise<Object>} 分析结果
   */
  async analyzeContent(content) {
    try {
      const prompt = `请分析以下内容的质量、主题和关键信息：\n\n${content.text || content}`;

      const response = await this.apiManager.callGeminiAPI({
        prompt: prompt,
        maxTokens: 500,
        temperature: 0.3
      });

      return {
        analysis: response.text,
        quality: this.assessContentQuality(content),
        wordCount: (content.text || content).split(/\s+/).length,
        timestamp: Date.now()
      };
    } catch (error) {
      throw new Error(`内容分析失败: ${error.message}`);
    }
  }

  /**
   * @function summarizeContent - 摘要内容
   * @description 生成内容摘要
   * @param {Object} content - 要摘要的内容
   * @returns {Promise<Object>} 摘要结果
   */
  async summarizeContent(content) {
    try {
      const prompt = `请为以下内容生成简洁的摘要：\n\n${content.text || content}`;

      const response = await this.apiManager.callGeminiAPI({
        prompt: prompt,
        maxTokens: 200,
        temperature: 0.2
      });

      return {
        summary: response.text,
        originalLength: (content.text || content).length,
        summaryLength: response.text.length,
        timestamp: Date.now()
      };
    } catch (error) {
      throw new Error(`内容摘要失败: ${error.message}`);
    }
  }

  /**
   * @function extractStructuredData - 提取结构化数据
   * @description 从页面提取结构化数据
   * @param {Object} parameters - 提取参数
   * @returns {Promise<Object>} 结构化数据
   */
  async extractStructuredData(parameters) {
    try {
      const { selectors = {}, format = 'json' } = parameters;
      const data = {};

      for (const [key, selector] of Object.entries(selectors)) {
        const elements = document.querySelectorAll(selector);
        data[key] = Array.from(elements).map(el => ({
          text: el.textContent.trim(),
          html: el.innerHTML,
          attributes: this.getElementAttributes(el)
        }));
      }

      return {
        data: data,
        format: format,
        extractedAt: Date.now(),
        url: window.location.href
      };
    } catch (error) {
      throw new Error(`结构化数据提取失败: ${error.message}`);
    }
  }

  /**
   * @function sendNotification - 发送通知
   * @description 发送通知消息
   * @param {Object} data - 通知数据
   * @param {Object} config - 通知配置
   * @returns {Promise<Object>} 发送结果
   */
  async sendNotification(data, config = {}) {
    try {
      const { title = 'AI工作流通知', message, type = 'info' } = config;

      const notificationData = {
        title: title,
        message: message || JSON.stringify(data),
        type: type,
        timestamp: Date.now()
      };

      // 发送Chrome通知 - 使用增强的通知创建机制
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        try {
          // 优先使用Service Worker的增强通知函数
          await chrome.runtime.sendMessage({
            type: 'ai:notification:create',
            data: {
              title: notificationData.title,
              message: notificationData.message
            }
          });
        } catch (error) {
          console.warn('[AI工作流] Service Worker通知失败，使用备用方案:', error.message);
          // 备用方案：直接创建通知，使用正确的图标文件名
          if (chrome.notifications) {
            await chrome.notifications.create({
              type: 'basic',
              iconUrl: 'assets/icons/icon-48.png',  // 修正：使用正确的文件名
              title: notificationData.title,
              message: notificationData.message
            });
          }
        }
      } else if (typeof chrome !== 'undefined' && chrome.notifications) {
        // 直接创建通知，使用正确的图标文件名
        await chrome.notifications.create({
          type: 'basic',
          iconUrl: 'assets/icons/icon-48.png',  // 修正：使用正确的文件名
          title: notificationData.title,
          message: notificationData.message
        });
      }

      return notificationData;
    } catch (error) {
      throw new Error(`通知发送失败: ${error.message}`);
    }
  }

  /**
   * @function saveSummary - 保存摘要
   * @description 保存摘要到存储
   * @param {Object} data - 摘要数据
   * @param {Object} config - 保存配置
   * @returns {Promise<Object>} 保存结果
   */
  async saveSummary(data, config = {}) {
    try {
      const { storageKey = 'ai_summaries' } = config;

      // 获取现有摘要
      const result = await chrome.storage.local.get([storageKey]);
      const summaries = result[storageKey] || [];

      // 添加新摘要
      const summaryEntry = {
        id: Date.now().toString(),
        data: data,
        timestamp: Date.now(),
        url: window.location.href
      };

      summaries.unshift(summaryEntry);

      // 限制数量
      if (summaries.length > 100) {
        summaries.splice(100);
      }

      // 保存
      await chrome.storage.local.set({ [storageKey]: summaries });

      return {
        saved: true,
        id: summaryEntry.id,
        totalSummaries: summaries.length
      };
    } catch (error) {
      throw new Error(`摘要保存失败: ${error.message}`);
    }
  }

  /**
   * @function exportToCsv - 导出为CSV
   * @description 将数据导出为CSV格式
   * @param {Object} data - 要导出的数据
   * @param {Object} config - 导出配置
   * @returns {Promise<Object>} 导出结果
   */
  async exportToCsv(data, config = {}) {
    try {
      const { filename = 'export.csv', delimiter = ',' } = config;

      // 转换数据为CSV格式
      let csvContent = '';

      if (Array.isArray(data)) {
        // 数组数据
        if (data.length > 0) {
          const headers = Object.keys(data[0]);
          csvContent += headers.join(delimiter) + '\n';

          data.forEach(row => {
            const values = headers.map(header => {
              const value = row[header] || '';
              return `"${value.toString().replace(/"/g, '""')}"`;
            });
            csvContent += values.join(delimiter) + '\n';
          });
        }
      } else if (typeof data === 'object') {
        // 对象数据
        csvContent += 'Key,Value\n';
        Object.entries(data).forEach(([key, value]) => {
          csvContent += `"${key}","${value.toString().replace(/"/g, '""')}"\n`;
        });
      }

      // 创建下载链接
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = filename;
      link.click();

      return {
        exported: true,
        filename: filename,
        size: blob.size
      };
    } catch (error) {
      throw new Error(`CSV导出失败: ${error.message}`);
    }
  }

  /**
   * @function sendWebhook - 发送Webhook
   * @description 发送数据到Webhook端点
   * @param {Object} data - 要发送的数据
   * @param {Object} config - Webhook配置
   * @returns {Promise<Object>} 发送结果
   */
  async sendWebhook(data, config = {}) {
    try {
      const { url, method = 'POST', headers = {} } = config;

      if (!url) {
        throw new Error('Webhook URL未配置');
      }

      const response = await fetch(url, {
        method: method,
        headers: {
          'Content-Type': 'application/json',
          ...headers
        },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        throw new Error(`Webhook请求失败: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();

      return {
        sent: true,
        status: response.status,
        response: result
      };
    } catch (error) {
      throw new Error(`Webhook发送失败: ${error.message}`);
    }
  }

  /**
   * @function validateWorkflow - 验证工作流
   * @description 验证工作流的有效性
   * @param {Object} workflow - 工作流对象
   * @returns {Object} 验证结果
   */
  validateWorkflow(workflow) {
    const errors = [];

    // 检查基本属性
    if (!workflow.name || typeof workflow.name !== 'string') {
      errors.push('工作流名称无效');
    }

    if (!workflow.nodes || !Array.isArray(workflow.nodes)) {
      errors.push('工作流节点无效');
    }

    if (!workflow.connections || !Array.isArray(workflow.connections)) {
      errors.push('工作流连接无效');
    }

    // 检查节点
    const nodeIds = new Set();
    let hasTrigger = false;

    for (const node of workflow.nodes) {
      if (!node.id || typeof node.id !== 'string') {
        errors.push('节点ID无效');
        continue;
      }

      if (nodeIds.has(node.id)) {
        errors.push(`重复的节点ID: ${node.id}`);
      }
      nodeIds.add(node.id);

      if (!this.nodeTypes.has(node.type)) {
        errors.push(`未知节点类型: ${node.type}`);
      }

      if (node.type === 'trigger') {
        hasTrigger = true;
      }
    }

    if (!hasTrigger) {
      errors.push('工作流必须包含至少一个触发器节点');
    }

    // 检查连接
    for (const connection of workflow.connections) {
      if (!connection.source || !nodeIds.has(connection.source)) {
        errors.push(`无效的连接源节点: ${connection.source}`);
      }

      if (!connection.target || !nodeIds.has(connection.target)) {
        errors.push(`无效的连接目标节点: ${connection.target}`);
      }
    }

    return {
      valid: errors.length === 0,
      errors: errors
    };
  }

  /**
   * @function processNodes - 处理节点数据
   * @description 处理和验证节点数据
   * @param {Array} nodes - 节点数组
   * @returns {Array} 处理后的节点数组
   */
  processNodes(nodes) {
    return nodes.map(node => ({
      id: node.id || this.generateNodeId(),
      type: node.type,
      config: node.config || {},
      position: node.position || { x: 0, y: 0 },
      metadata: {
        created: Date.now(),
        ...node.metadata
      }
    }));
  }

  /**
   * @function processConnections - 处理连接数据
   * @description 处理和验证连接数据
   * @param {Array} connections - 连接数组
   * @returns {Array} 处理后的连接数组
   */
  processConnections(connections) {
    return connections.map(connection => ({
      id: connection.id || this.generateConnectionId(),
      source: connection.source,
      target: connection.target,
      condition: connection.condition || null,
      metadata: {
        created: Date.now(),
        ...connection.metadata
      }
    }));
  }

  /**
   * @function generateWorkflowId - 生成工作流ID
   * @description 生成唯一的工作流ID
   * @returns {string} 工作流ID
   */
  generateWorkflowId() {
    return `workflow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * @function generateExecutionId - 生成执行ID
   * @description 生成唯一的执行ID
   * @returns {string} 执行ID
   */
  generateExecutionId() {
    return `execution_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * @function generateNodeId - 生成节点ID
   * @description 生成唯一的节点ID
   * @returns {string} 节点ID
   */
  generateNodeId() {
    return `node_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * @function generateConnectionId - 生成连接ID
   * @description 生成唯一的连接ID
   * @returns {string} 连接ID
   */
  generateConnectionId() {
    return `connection_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * @function getStats - 获取统计信息
   * @description 获取工作流引擎的统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      ...this.stats,
      activeWorkflows: this.workflows.size,
      runningExecutions: this.activeExecutions.size,
      scheduledWorkflows: this.scheduledWorkflows.size,
      successRate: this.stats.executedWorkflows > 0 ?
        (this.stats.successfulExecutions / this.stats.executedWorkflows * 100).toFixed(2) + '%' : '0%'
    };
  }

  /**
   * @function cleanup - 清理资源
   * @description 清理工作流引擎使用的资源
   */
  cleanup() {
    // 停止所有活跃执行
    for (const execution of this.activeExecutions.values()) {
      execution.status = 'cancelled';
    }
    this.activeExecutions.clear();

    // 清理调度器
    this.scheduledWorkflows.clear();

    // 清理触发器
    this.activeTriggers.clear();
    this.triggers.clear();

    // 清理工作流
    this.workflows.clear();

    console.log('[工作流] AI工作流引擎已清理');
  }
}

// 导出工作流引擎类
export { AiWorkflowEngine };