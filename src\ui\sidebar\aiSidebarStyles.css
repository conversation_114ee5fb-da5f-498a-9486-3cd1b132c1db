/**
 * @file AI侧边栏样式文件
 * @description 侧边栏UI组件的完整样式定义，采用BEM命名规范
 */

/* #region 基础变量和重置 */
:root {
  /* AI侧边栏主题色彩 */
  --ai-primary-color: #2563eb;
  --ai-primary-color-light: #3b82f6;
  --ai-primary-color-dark: #1d4ed8;
  --ai-secondary-color: #64748b;
  --ai-accent-color: #8b5cf6;
  --ai-success-color: #10b981;
  --ai-warning-color: #f59e0b;
  --ai-danger-color: #ef4444;
  
  /* 背景色彩 */
  --ai-bg-primary: #ffffff;
  --ai-bg-secondary: #f8fafc;
  --ai-bg-tertiary: #f1f5f9;
  --ai-bg-hover: #f1f5f9;
  --ai-bg-active: #e2e8f0;
  --ai-bg-dark: #0f172a;
  --ai-bg-dark-secondary: #1e293b;
  
  /* 文本色彩 */
  --ai-text-primary: #0f172a;
  --ai-text-secondary: #64748b;
  --ai-text-muted: #94a3b8;
  --ai-text-white: #ffffff;
  --ai-text-success: #059669;
  --ai-text-warning: #d97706;
  --ai-text-danger: #dc2626;
  
  /* 边框和分割线 */
  --ai-border-color: #e2e8f0;
  --ai-border-color-light: #f1f5f9;
  --ai-border-color-dark: #334155;
  
  /* 阴影 */
  --ai-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --ai-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  --ai-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
  --ai-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
  
  /* 圆角 */
  --ai-radius-sm: 4px;
  --ai-radius-md: 8px;
  --ai-radius-lg: 12px;
  --ai-radius-xl: 16px;
  
  /* 间距 */
  --ai-spacing-xs: 4px;
  --ai-spacing-sm: 8px;
  --ai-spacing-md: 16px;
  --ai-spacing-lg: 24px;
  --ai-spacing-xl: 32px;
  --ai-spacing-2xl: 48px;
  
  /* 动画 */
  --ai-transition-fast: 0.15s ease-out;
  --ai-transition-normal: 0.25s ease-out;
  --ai-transition-slow: 0.35s ease-out;
  
  /* 侧边栏尺寸 - 优化空间利用 */
  --ai-sidebar-width: 380px;
  --ai-sidebar-width-collapsed: 60px;
  --ai-header-height: 40px; /* 减小顶部栏高度 */
  --ai-templates-height: 44px; /* 模板栏高度 */
  --ai-input-area-height: 80px; /* 输入区域高度 */
  --ai-footer-height: 0px; /* 移除底部栏 */
  
  /* 字体大小 */
  --ai-text-xs: 12px;
  --ai-text-sm: 14px;
  --ai-text-base: 16px;
  --ai-text-lg: 18px;
  --ai-text-xl: 20px;
  
  /* Z-index层级 */
  --ai-z-sidebar: 999999;
  --ai-z-modal: 1000000;
  --ai-z-tooltip: 1000001;
}

* {
  box-sizing: border-box;
}

.ai-sidebar {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  line-height: 1.5;
}
/* #endregion */

/* #region 主容器 */
.ai-sidebar {
  position: fixed;
  top: 0;
  right: 0;
  width: var(--ai-sidebar-width);
  height: 100vh;
  background: var(--ai-bg-primary);
  border-left: 1px solid var(--ai-border-color);
  box-shadow: var(--ai-shadow-lg);
  z-index: var(--ai-z-sidebar);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: transform var(--ai-transition-normal);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: var(--ai-text-sm);
  line-height: 1.5;
}

.ai-sidebar--collapsed {
  transform: translateX(calc(var(--ai-sidebar-width) - var(--ai-sidebar-width-collapsed)));
}

.ai-sidebar--hidden {
  transform: translateX(100%);
}
/* #endregion */

/* #region 精简顶部栏 - 最小化空间占用 */
.ai-sidebar__header {
  height: var(--ai-header-height); /* 使用变量控制高度 */
  padding: 8px 12px; /* 减小内边距 */
  background: var(--ai-bg-primary);
  border-bottom: 1px solid var(--ai-border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
}

.ai-sidebar__logo {
  display: flex;
  align-items: center;
  gap: var(--ai-spacing-sm);
}

.ai-sidebar__logo-icon {
  font-size: var(--ai-text-lg);
  line-height: 1;
}

.ai-sidebar__logo-text {
  font-weight: 600;
  font-size: var(--ai-text-base);
  color: var(--ai-text-primary);
}

.ai-sidebar__header-actions {
  display: flex;
  align-items: center;
}

.ai-sidebar__action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--ai-radius-md);
  background: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--ai-text-secondary);
  transition: all var(--ai-transition-fast);
}

.ai-sidebar__action-btn:hover {
  background: var(--ai-bg-hover);
  color: var(--ai-text-primary);
}

.ai-sidebar__action-btn:active {
  background: var(--ai-bg-active);
}

.ai-sidebar__action-btn .ai-icon {
  font-size: var(--ai-text-base);
  line-height: 1;
}

.ai-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  min-width: 16px;
  height: 16px;
  background: var(--ai-danger-color);
  color: var(--ai-text-white);
  border-radius: 8px;
  font-size: 10px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 4px;
}
/* #endregion */

/* #region 快捷模板系统 */
.ai-templates-bar {
  display: flex;
  align-items: center;
  padding: var(--ai-spacing-sm) var(--ai-spacing-md);
  background: var(--ai-bg-secondary);
  border-bottom: 1px solid var(--ai-border-color);
  flex-shrink: 0;
  gap: var(--ai-spacing-sm);
}

.ai-templates-scroll {
  display: flex;
  align-items: center;
  gap: var(--ai-spacing-sm);
  flex: 1;
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE */
}

.ai-templates-scroll::-webkit-scrollbar {
  display: none; /* Chrome, Safari */
}

.ai-template-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  padding: var(--ai-spacing-xs);
  border: none;
  background: var(--ai-bg-primary);
  border-radius: var(--ai-radius-md);
  cursor: pointer;
  transition: all var(--ai-transition-fast);
  min-width: 60px;
  flex-shrink: 0;
  box-shadow: var(--ai-shadow-sm);
}

.ai-template-item:hover {
  background: var(--ai-primary-color);
  color: var(--ai-text-white);
  transform: translateY(-1px);
  box-shadow: var(--ai-shadow-md);
}

.ai-template-item:active {
  transform: translateY(0);
}

.ai-template-icon {
  font-size: var(--ai-text-base);
  line-height: 1;
}

.ai-template-name {
  font-size: 10px;
  font-weight: 500;
  line-height: 1;
  text-align: center;
}

.ai-templates-more {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: var(--ai-bg-primary);
  border-radius: var(--ai-radius-md);
  cursor: pointer;
  transition: all var(--ai-transition-fast);
  color: var(--ai-text-secondary);
  flex-shrink: 0;
}

.ai-templates-more:hover {
  background: var(--ai-bg-hover);
  color: var(--ai-text-primary);
}

.ai-templates-more .ai-icon {
  font-size: var(--ai-text-base);
  line-height: 1;
}
/* #endregion */

/* #region 主内容区域 - 对话中心化设计 */
.ai-sidebar__main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
  /* 计算高度：100vh - 顶部栏 - 模板栏 - 输入区域 */
  height: calc(100vh - var(--ai-header-height) - var(--ai-templates-height) - var(--ai-input-area-height));
}
/* #endregion */

/* #region 对话区域 - 占据主要空间 */
.ai-chat {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
}

.ai-chat__messages {
  flex: 1;
  padding: 12px; /* 减小内边距，增加内容空间 */
  overflow-y: auto;
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: var(--ai-border-color) transparent;
  /* 确保消息区域占据所有可用空间 */
  min-height: 0;
}

.ai-chat__messages::-webkit-scrollbar {
  width: 6px;
}

.ai-chat__messages::-webkit-scrollbar-track {
  background: transparent;
}

.ai-chat__messages::-webkit-scrollbar-thumb {
  background: var(--ai-border-color);
  border-radius: 3px;
}

.ai-chat__messages::-webkit-scrollbar-thumb:hover {
  background: var(--ai-text-muted);
}

.ai-chat__message {
  margin-bottom: var(--ai-spacing-md);
  display: flex;
  flex-direction: column;
  gap: var(--ai-spacing-xs);
}

.ai-chat__message--user {
  align-items: flex-end;
}

.ai-chat__message--assistant {
  align-items: flex-start;
}

.ai-chat__bubble {
  position: relative;
  max-width: 85%;
  background: var(--ai-bg-secondary);
  border-radius: var(--ai-radius-lg);
  border: 1px solid var(--ai-border-color);
  overflow: hidden;
  transition: all var(--ai-transition-fast);
}

.ai-chat__message--user .ai-chat__bubble {
  background: var(--ai-primary-color);
  border-color: var(--ai-primary-color);
}

.ai-chat__bubble:hover {
  box-shadow: var(--ai-shadow-md);
}

.ai-chat__bubble:hover .ai-chat__hover-actions {
  opacity: 1;
  visibility: visible;
}

.ai-chat__content {
  padding: 12px 16px; /* 优化内边距 */
}

.ai-chat__text {
  line-height: 1.6;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-size: var(--ai-text-sm);
  color: var(--ai-text-primary);
}

.ai-chat__message--user .ai-chat__text {
  color: var(--ai-text-white);
}

/* 悬浮操作菜单 - 鼠标悬停显示 */
.ai-chat__hover-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 4px;
  opacity: 0;
  visibility: hidden;
  transition: all var(--ai-transition-fast);
  background: rgba(255, 255, 255, 0.95);
  border-radius: var(--ai-radius-md);
  padding: 4px;
  box-shadow: var(--ai-shadow-sm);
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
  border: 1px solid var(--ai-border-color-light);
}

.ai-chat__message--user .ai-chat__actions {
  background: rgba(0, 0, 0, 0.2);
}

.ai-action-btn {
  width: 24px;
  height: 24px;
  border: none;
  border-radius: var(--ai-radius-sm);
  background: transparent;
  color: var(--ai-text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--ai-transition-fast);
  font-size: 12px;
}

.ai-action-btn:hover {
  background: var(--ai-bg-hover);
  color: var(--ai-text-primary);
  transform: scale(1.1);
}

.ai-chat__message--user .ai-action-btn {
  color: rgba(255, 255, 255, 0.8);
}

.ai-chat__message--user .ai-action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: var(--ai-text-white);
}

.ai-chat__time {
  font-size: var(--ai-text-xs);
  color: var(--ai-text-muted);
  padding: 0 var(--ai-spacing-sm);
  margin-top: 2px;
}

/* 输入区域样式 - 紧凑设计 */
.ai-chat__input-area {
  padding: 12px;
  border-top: 1px solid var(--ai-border-color);
  background: var(--ai-bg-primary);
  flex-shrink: 0;
  height: var(--ai-input-area-height);
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 多风格回复选择器 - 紧凑设计 */
.ai-reply-style-selector {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.ai-reply-style-select,
.ai-language-select {
  flex: 1;
  padding: 6px 10px;
  border: 1px solid var(--ai-border-color);
  border-radius: var(--ai-radius-sm);
  background: var(--ai-bg-secondary);
  color: var(--ai-text-primary);
  font-size: 12px;
  cursor: pointer;
  transition: all var(--ai-transition-fast);
  min-height: 28px;
}

.ai-reply-style-select:focus,
.ai-language-select:focus {
  outline: none;
  border-color: var(--ai-primary-color);
  box-shadow: 0 0 0 2px rgb(37 99 235 / 0.1);
  background: var(--ai-bg-primary);
}

.ai-reply-style-select:hover,
.ai-language-select:hover {
  border-color: var(--ai-primary-color-light);
  background: var(--ai-bg-primary);
}

.ai-language-selector {
  flex: 0 0 auto;
  min-width: 80px;
}

/* 输入框容器 - 简化设计 */
.ai-chat__input-container {
  flex: 1;
}

.ai-chat__input {
  width: 100%;
  min-height: 36px;
  max-height: 100px;
  padding: 10px 12px;
  border: 1px solid var(--ai-border-color);
  border-radius: var(--ai-radius-md);
  background: var(--ai-bg-secondary);
  color: var(--ai-text-primary);
  font-size: var(--ai-text-sm);
  font-family: inherit;
  resize: none;
  outline: none;
  transition: all var(--ai-transition-fast);
  line-height: 1.4;
}

.ai-chat__input:focus {
  border-color: var(--ai-primary-color);
  box-shadow: 0 0 0 2px rgb(37 99 235 / 0.1);
  background: var(--ai-bg-primary);
}

.ai-chat__input::placeholder {
  color: var(--ai-text-muted);
  font-size: 12px;
}
/* #endregion */

/* 通用图标样式 */
.ai-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  -webkit-user-select: none;
  user-select: none;
  font-family: 'Segoe UI Symbol', 'Apple Color Emoji', 'Segoe UI Emoji', 'Noto Color Emoji';
}

/* 占位符组件 */
.ai-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--ai-spacing-xl);
  text-align: center;
  color: var(--ai-text-muted);
}

.ai-placeholder__icon {
  font-size: 48px;
  margin-bottom: var(--ai-spacing-md);
  opacity: 0.6;
}

.ai-placeholder__title {
  font-size: var(--ai-text-lg);
  font-weight: 600;
  color: var(--ai-text-secondary);
  margin: 0 0 var(--ai-spacing-sm) 0;
}

.ai-placeholder__text {
  font-size: var(--ai-text-sm);
  line-height: 1.5;
  margin: 0;
  max-width: 280px;
}

/* 按钮组件 */
.ai-btn {
  padding: var(--ai-spacing-sm) var(--ai-spacing-md);
  border: 1px solid var(--ai-border-color);
  border-radius: var(--ai-radius-md);
  background: var(--ai-bg-primary);
  color: var(--ai-text-primary);
  font-size: var(--ai-text-sm);
  font-weight: 500;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: var(--ai-spacing-xs);
  transition: all var(--ai-transition-fast);
  text-decoration: none;
}

.ai-btn:hover {
  border-color: var(--ai-primary-color);
  color: var(--ai-primary-color);
  transform: translateY(-1px);
  box-shadow: var(--ai-shadow-sm);
}

.ai-btn--primary {
  background: var(--ai-primary-color);
  color: var(--ai-text-white);
  border-color: var(--ai-primary-color);
}

.ai-btn--primary:hover {
  background: var(--ai-primary-color-dark);
  border-color: var(--ai-primary-color-dark);
  color: var(--ai-text-white);
}

.ai-btn--danger {
  background: var(--ai-danger-color);
  color: var(--ai-text-white);
  border-color: var(--ai-danger-color);
}

.ai-btn--danger:hover {
  background: #dc2626;
  border-color: #dc2626;
  color: var(--ai-text-white);
}

.ai-btn--small {
  padding: 6px 12px;
  font-size: 12px;
}

.ai-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 表单控件 */
.ai-select {
  padding: var(--ai-spacing-sm);
  border: 1px solid var(--ai-border-color);
  border-radius: var(--ai-radius-md);
  background: var(--ai-bg-primary);
  color: var(--ai-text-primary);
  font-size: var(--ai-text-sm);
  cursor: pointer;
  transition: border-color var(--ai-transition-fast);
}

.ai-select:focus {
  outline: none;
  border-color: var(--ai-primary-color);
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.ai-range {
  -webkit-appearance: none;
  appearance: none;
  width: 120px;
  height: 6px;
  border-radius: 3px;
  background: var(--ai-bg-tertiary);
  outline: none;
  border: 1px solid var(--ai-border-color);
}

.ai-range::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--ai-primary-color);
  cursor: pointer;
  transition: transform var(--ai-transition-fast);
}

.ai-range::-webkit-slider-thumb:hover {
  transform: scale(1.2);
}

/* 复选框组件 */
.ai-checkbox {
  display: flex;
  align-items: center;
  gap: var(--ai-spacing-sm);
  cursor: pointer;
  font-size: var(--ai-text-sm);
  color: var(--ai-text-primary);
}

.ai-checkbox input[type="checkbox"] {
  display: none;
}

.ai-checkbox__mark {
  width: 16px;
  height: 16px;
  border: 2px solid var(--ai-border-color);
  border-radius: var(--ai-radius-sm);
  background: var(--ai-bg-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--ai-transition-fast);
  flex-shrink: 0;
}

.ai-checkbox input[type="checkbox"]:checked + .ai-checkbox__mark {
  background: var(--ai-primary-color);
  border-color: var(--ai-primary-color);
}

.ai-checkbox input[type="checkbox"]:checked + .ai-checkbox__mark::after {
  content: '✓';
  color: var(--ai-text-white);
  font-size: 10px;
  font-weight: bold;
}

.ai-checkbox:hover .ai-checkbox__mark {
  border-color: var(--ai-primary-color);
}

.ai-checkbox-group {
  display: flex;
  flex-direction: column;
  gap: var(--ai-spacing-sm);
}
/* #endregion */

/* #region 对话面板 */
.ai-chat {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.ai-chat__messages {
  flex: 1;
  padding: var(--ai-spacing-md);
  overflow-y: auto;
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: var(--ai-border-color) transparent;
}

.ai-chat__messages::-webkit-scrollbar {
  width: 6px;
}

.ai-chat__messages::-webkit-scrollbar-track {
  background: transparent;
}

.ai-chat__messages::-webkit-scrollbar-thumb {
  background: var(--ai-border-color);
  border-radius: 3px;
}

.ai-chat__messages::-webkit-scrollbar-thumb:hover {
  background: var(--ai-text-muted);
}

.ai-chat__message {
  margin-bottom: var(--ai-spacing-md);
  display: flex;
  gap: var(--ai-spacing-sm);
}

.ai-chat__message--user {
  flex-direction: row-reverse;
}

.ai-chat__message--assistant {
  flex-direction: row;
}

.ai-chat__avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--ai-bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--ai-text-sm);
  flex-shrink: 0;
  margin-top: var(--ai-spacing-xs);
}

.ai-chat__message--user .ai-chat__avatar {
  background: var(--ai-primary-color);
  color: var(--ai-text-white);
}

.ai-chat__message--assistant .ai-chat__avatar {
  background: var(--ai-accent-color);
  color: var(--ai-text-white);
}

.ai-chat__message-content {
  max-width: calc(100% - 48px);
  display: flex;
  flex-direction: column;
  gap: var(--ai-spacing-xs);
}

.ai-chat__message-text {
  padding: var(--ai-spacing-sm) var(--ai-spacing-md);
  border-radius: var(--ai-radius-lg);
  line-height: 1.6;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-size: var(--ai-text-sm);
}

.ai-chat__message--user .ai-chat__message-text {
  background: var(--ai-primary-color);
  color: var(--ai-text-white);
  border-bottom-right-radius: var(--ai-radius-sm);
}

.ai-chat__message--assistant .ai-chat__message-text {
  background: var(--ai-bg-secondary);
  color: var(--ai-text-primary);
  border-bottom-left-radius: var(--ai-radius-sm);
  border: 1px solid var(--ai-border-color-light);
}

.ai-chat__message-time {
  font-size: var(--ai-text-xs);
  color: var(--ai-text-muted);
  padding: 0 var(--ai-spacing-md);
}

.ai-chat__input-area {
  padding: var(--ai-spacing-md);
  border-top: 1px solid var(--ai-border-color);
  background: var(--ai-bg-primary);
  flex-shrink: 0;
}

.ai-chat__input-container {
  display: flex;
  gap: var(--ai-spacing-sm);
  align-items: flex-end;
}

.ai-chat__input-wrapper {
  flex: 1;
  position: relative;
  border: 1px solid var(--ai-border-color);
  border-radius: var(--ai-radius-md);
  background: var(--ai-bg-primary);
  transition: border-color var(--ai-transition-fast);
}

.ai-chat__input-wrapper:focus-within {
  border-color: var(--ai-primary-color);
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.ai-chat__input {
  width: 100%;
  min-height: 36px;
  max-height: 120px;
  padding: var(--ai-spacing-sm) var(--ai-spacing-md);
  padding-right: 80px;
  border: none;
  background: transparent;
  color: var(--ai-text-primary);
  font-size: var(--ai-text-sm);
  font-family: inherit;
  resize: none;
  outline: none;
  line-height: 1.4;
}

.ai-chat__input::placeholder {
  color: var(--ai-text-muted);
}

.ai-chat__input-actions {
  position: absolute;
  right: var(--ai-spacing-sm);
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  gap: var(--ai-spacing-xs);
}

.ai-chat__action-btn {
  width: 24px;
  height: 24px;
  border: none;
  border-radius: var(--ai-radius-sm);
  background: transparent;
  color: var(--ai-text-muted);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--ai-transition-fast);
}

.ai-chat__action-btn:hover {
  background: var(--ai-bg-hover);
  color: var(--ai-text-secondary);
}

.ai-chat__send-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: var(--ai-radius-md);
  background: var(--ai-primary-color);
  color: var(--ai-text-white);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--ai-transition-fast);
  flex-shrink: 0;
}

.ai-chat__send-btn:hover {
  background: var(--ai-primary-color-dark);
  transform: scale(1.05);
}

.ai-chat__send-btn:active {
  transform: scale(0.95);
}

.ai-chat__send-btn:disabled {
  background: var(--ai-text-muted);
  cursor: not-allowed;
  transform: none;
}

.ai-chat__suggestions {
  margin-top: var(--ai-spacing-sm);
  padding: var(--ai-spacing-sm);
  background: var(--ai-bg-secondary);
  border-radius: var(--ai-radius-md);
  border: 1px solid var(--ai-border-color);
}

.ai-chat__suggestion-item {
  padding: var(--ai-spacing-xs) var(--ai-spacing-sm);
  border-radius: var(--ai-radius-sm);
  cursor: pointer;
  font-size: var(--ai-text-sm);
  color: var(--ai-text-secondary);
  transition: all var(--ai-transition-fast);
}

.ai-chat__suggestion-item:hover,
.ai-chat__suggestion-item--active {
  background: var(--ai-primary-color);
  color: var(--ai-text-white);
}
/* #endregion */

/* #region 分析面板 */
.ai-analysis {
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 允许flex子元素收缩 */
}

.ai-analysis__controls {
  padding: var(--ai-spacing-md);
  border-bottom: 1px solid var(--ai-border-color);
}

.ai-sidebar__btn {
  width: 100%;
  padding: var(--ai-spacing-md);
  border: 1px solid var(--ai-primary-color);
  border-radius: var(--ai-radius-md);
  background: var(--ai-primary-color);
  color: var(--ai-text-white);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--ai-transition-fast);
}

.ai-sidebar__btn:hover {
  background: var(--ai-primary-color-light);
}

.ai-analysis__results {
  flex: 1;
  padding: var(--ai-spacing-md);
  overflow-y: auto; /* 允许结果区域滚动 */
  min-height: 0; /* 确保可以收缩 */
  scrollbar-width: thin; /* Firefox滚动条样式 */
  scrollbar-color: var(--ai-border-color) transparent;
}

/* 分析结果区域的Webkit滚动条样式 */
.ai-analysis__results::-webkit-scrollbar {
  width: 6px;
}

.ai-analysis__results::-webkit-scrollbar-track {
  background: transparent;
}

.ai-analysis__results::-webkit-scrollbar-thumb {
  background: var(--ai-border-color);
  border-radius: 3px;
}

.ai-analysis__results::-webkit-scrollbar-thumb:hover {
  background: var(--ai-text-muted);
}

.ai-analysis__placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 确保占位符可以正确收缩 */
  align-items: center;
  justify-content: center;
  color: var(--ai-text-muted);
  text-align: center;
}

.ai-sidebar__icon {
  width: 20px;
  height: 20px;
  fill: currentColor;
}

.ai-sidebar__icon--large {
  width: 48px;
  height: 48px;
  margin-bottom: var(--ai-spacing-md);
}
/* #endregion */

/* #region 状态栏 */
.ai-sidebar__footer {
  height: var(--ai-footer-height);
  padding: 0 var(--ai-spacing-md);
  background: var(--ai-bg-secondary);
  border-top: 1px solid var(--ai-border-color);
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.ai-sidebar__status {
  font-size: 12px;
  color: var(--ai-text-muted);
}
/* #endregion */

/* #region 设置模态框样式 */
.ai-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ai-modal__overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
}

.ai-modal__content {
  position: relative;
  background: var(--ai-bg-primary);
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--ai-border-color);
}

.ai-modal__content--small {
  width: 400px;
  min-height: 200px;
}

.ai-modal__content--large {
  width: 900px;
  min-height: 600px;
}

.ai-modal__header {
  padding: 20px 24px;
  border-bottom: 1px solid var(--ai-border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--ai-bg-secondary);
}

.ai-modal__title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--ai-text-primary);
}

.ai-modal__close-btn {
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.ai-modal__close-btn:hover {
  background-color: var(--ai-bg-hover);
}

.ai-modal__body {
  flex: 1;
  overflow: hidden;
  display: flex;
}

.ai-modal__footer {
  padding: 16px 24px;
  border-top: 1px solid var(--ai-border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--ai-bg-secondary);
}

.ai-modal__footer-actions {
  display: flex;
  gap: 8px;
}

/* 设置导航 */
.ai-settings-nav {
  width: 200px;
  background: var(--ai-bg-tertiary);
  border-right: 1px solid var(--ai-border-color);
  overflow-y: auto;
  padding: 16px 0;
}

.ai-settings-nav__item {
  display: block;
  width: 100%;
  padding: 12px 20px;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  font-size: 14px;
  color: var(--ai-text-secondary);
  transition: all 0.2s;
  border-left: 3px solid transparent;
}

.ai-settings-nav__item:hover {
  background-color: var(--ai-bg-hover);
  color: var(--ai-text-primary);
}

.ai-settings-nav__item--active {
  background-color: var(--ai-accent-bg);
  color: var(--ai-accent-color);
  border-left-color: var(--ai-accent-color);
  font-weight: 500;
}

/* 设置内容区域 */
.ai-settings-content {
  flex: 1;
  overflow-y: auto; /* 允许设置内容滚动 */
  padding: 24px;
  min-height: 0; /* 确保可以收缩 */
  scrollbar-width: thin; /* Firefox滚动条样式 */
  scrollbar-color: var(--ai-border-color) transparent;
}

/* 设置内容区域的Webkit滚动条样式 */
.ai-settings-content::-webkit-scrollbar {
  width: 6px;
}

.ai-settings-content::-webkit-scrollbar-track {
  background: transparent;
}

.ai-settings-content::-webkit-scrollbar-thumb {
  background: var(--ai-border-color);
  border-radius: 3px;
}

.ai-settings-content::-webkit-scrollbar-thumb:hover {
  background: var(--ai-text-muted);
}

.ai-settings-panel {
  display: none;
}

.ai-settings-panel--active {
  display: block;
}

/* 设置组件样式 */
.ai-settings__group {
  margin-bottom: 32px;
}

.ai-settings__group-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--ai-text-primary);
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid var(--ai-accent-color);
}

.ai-settings__item {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 16px 0;
  border-bottom: 1px solid var(--ai-border-light);
}

.ai-settings__item:last-child {
  border-bottom: none;
}

.ai-settings__item-info {
  flex: 1;
  margin-right: 20px;
}

.ai-settings__item-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--ai-text-primary);
  margin-bottom: 4px;
  display: block;
}

.ai-settings__item-desc {
  font-size: 12px;
  color: var(--ai-text-secondary);
  line-height: 1.4;
}

.ai-settings__control {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 200px;
  justify-content: flex-end;
}

/* 开关组件 */
.ai-switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.ai-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.ai-switch__slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--ai-bg-tertiary);
  transition: 0.3s;
  border-radius: 24px;
  border: 1px solid var(--ai-border-color);
}

.ai-switch__slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.ai-switch input:checked + .ai-switch__slider {
  background-color: var(--ai-accent-color);
  border-color: var(--ai-accent-color);
}

.ai-switch input:checked + .ai-switch__slider:before {
  transform: translateX(20px);
}

/* 范围滑块 */
.ai-range {
  -webkit-appearance: none;
  appearance: none;
  width: 120px;
  height: 6px;
  border-radius: 3px;
  background: var(--ai-bg-tertiary);
  outline: none;
  border: 1px solid var(--ai-border-color);
}

.ai-range::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--ai-primary-color);
  cursor: pointer;
  transition: transform var(--ai-transition-fast);
}

.ai-range::-webkit-slider-thumb:hover {
  transform: scale(1.2);
}

.ai-settings__range-value {
  font-size: 12px;
  color: var(--ai-text-secondary);
  min-width: 40px;
  text-align: right;
}

/* 复选框组 */
.ai-checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.ai-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 13px;
  color: var(--ai-text-primary);
}

.ai-checkbox input {
  display: none;
}

.ai-checkbox__mark {
  width: 16px;
  height: 16px;
  border: 2px solid var(--ai-border-color);
  border-radius: 3px;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  background: var(--ai-bg-primary);
}

.ai-checkbox input:checked + .ai-checkbox__mark {
  background-color: var(--ai-accent-color);
  border-color: var(--ai-accent-color);
}

.ai-checkbox input:checked + .ai-checkbox__mark::after {
  content: "✓";
  color: white;
  font-size: 10px;
  font-weight: bold;
}

.ai-checkbox__label {
  flex: 1;
}

/* 输入框小尺寸 */
.ai-input--small {
  width: 80px;
  padding: 6px 8px;
  font-size: 12px;
}

/* 输入框错误状态 */
.ai-input--error {
  border-color: #ef4444;
  background-color: rgba(239, 68, 68, 0.05);
}

.ai-input--error:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* 密码切换组件 */
.ai-password-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.ai-password-toggle {
  position: absolute;
  right: 8px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  font-size: 14px;
  border-radius: 3px;
  transition: background-color 0.2s;
}

.ai-password-toggle:hover {
  background-color: var(--ai-bg-hover);
}

/* 次要按钮样式 */
.ai-btn--secondary {
  background-color: var(--ai-bg-secondary);
  color: var(--ai-text-primary);
  border-color: var(--ai-border-color);
}

.ai-btn--secondary:hover {
  background-color: var(--ai-bg-tertiary);
  border-color: var(--ai-text-secondary);
}

/* 危险按钮样式 */
.ai-btn--danger {
  background-color: #ef4444;
  color: white;
  border-color: #ef4444;
}

.ai-btn--danger:hover {
  background-color: #dc2626;
  border-color: #dc2626;
}

/* 通知面板 */
.ai-notifications-panel {
  position: absolute;
  top: 50px;
  right: 10px;
  width: 300px;
  max-height: 400px;
  background: var(--ai-bg-primary);
  border: 1px solid var(--ai-border-color);
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
}

.ai-notifications-panel__header {
  padding: 12px 16px;
  border-bottom: 1px solid var(--ai-border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--ai-bg-secondary);
}

.ai-notifications-panel__header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--ai-text-primary);
}

.ai-notifications-panel__list {
  max-height: 320px;
  overflow-y: auto;
  padding: 8px;
}

/* 加载指示器 */
.ai-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10001;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ai-loading__backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  -webkit-backdrop-filter: blur(2px);
  backdrop-filter: blur(2px);
}

.ai-loading__content {
  position: relative;
  background: var(--ai-bg-primary);
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  border: 1px solid var(--ai-border-color);
}

.ai-loading__spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--ai-border-color);
  border-top: 3px solid var(--ai-accent-color);
  border-radius: 50%;
  animation: ai-spin 1s linear infinite;
}

.ai-loading__text {
  font-size: 14px;
  color: var(--ai-text-secondary);
}

@keyframes ai-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 键盘按键样式 */
.ai-kbd {
  display: inline-block;
  padding: 2px 6px;
  font-size: 11px;
  line-height: 1.4;
  color: var(--ai-text-primary);
  background-color: var(--ai-bg-secondary);
  border: 1px solid var(--ai-border-color);
  border-radius: 3px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* 响应式设计 - 优化不同窗口尺寸 */
@media (max-width: 768px) {
  /* 侧边栏在小屏幕上的适配 */
  .ai-sidebar {
    width: 100vw;
    right: 0;
  }

  .ai-sidebar__header {
    height: 44px;
    padding: 8px 16px;
  }

  .ai-templates-bar {
    padding: 8px 12px;
  }

  .ai-template-item {
    min-width: 60px;
  }

  .ai-template-name {
    font-size: 11px;
  }

  .ai-chat__messages {
    padding: 8px;
  }

  .ai-chat__input-area {
    padding: 8px;
    height: auto;
    min-height: 60px;
  }

  .ai-reply-style-selector {
    flex-direction: column;
    gap: 6px;
  }

  .ai-modal__content--large {
    width: 95vw;
    height: 90vh;
  }
}

@media (max-width: 480px) {
  /* 超小屏幕优化 */
  .ai-sidebar__logo-text {
    display: none;
  }

  .ai-templates-bar {
    display: none; /* 在超小屏幕隐藏模板栏 */
  }

  .ai-chat__bubble {
    max-width: 95%;
  }

  .ai-chat__input {
    font-size: 16px; /* 防止iOS缩放 */
  }
}

@media (min-width: 1200px) {
  /* 大屏幕优化 */
  .ai-sidebar {
    width: 420px;
  }

  .ai-chat__messages {
    padding: 16px;
  }

  .ai-chat__bubble {
    max-width: 80%;
  }
}
/* #endregion */

/* #region 动画效果 */
@keyframes ai-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

@keyframes ai-blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.3;
  }
}

@keyframes ai-fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes ai-slide-in {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}
/* #endregion */

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --ai-bg-primary: var(--ai-bg-dark);
    --ai-bg-secondary: var(--ai-bg-dark-secondary);
    --ai-bg-tertiary: #334155;
    --ai-text-primary: #f8fafc;
    --ai-text-secondary: #cbd5e1;
    --ai-text-muted: #94a3b8;
    --ai-border-color: var(--ai-border-color-dark);
  }
}

/* #region 分析面板样式 */
/* 分析控制面板 */
.ai-analysis__controls {
  padding: 16px;
  background: var(--ai-bg-secondary);
  border-bottom: 1px solid var(--ai-border-color);
  margin-bottom: 16px;
}

.ai-analysis__options {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 16px;
}

.ai-analysis__depth {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ai-analysis__depth .ai-setting-item__label {
  font-size: 12px;
  color: var(--ai-text-secondary);
  margin: 0;
}

.ai-select--small {
  padding: 6px 8px;
  font-size: 12px;
  min-width: 120px;
}

/* 页面信息卡片 */
.ai-page-info {
  margin: 16px;
  padding: 16px;
  background: var(--ai-bg-secondary);
  border: 1px solid var(--ai-border-color);
  border-radius: 8px;
}

.ai-page-info__header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.ai-page-info__icon {
  font-size: 20px;
  flex-shrink: 0;
}

.ai-page-info__details {
  flex: 1;
  min-width: 0;
}

.ai-page-info__title {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--ai-text-primary);
  line-height: 1.3;
}

.ai-page-info__url {
  font-size: 12px;
  color: var(--ai-text-secondary);
  margin-bottom: 8px;
  word-break: break-all;
}

.ai-page-info__meta {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.ai-meta-item {
  font-size: 11px;
  color: var(--ai-text-muted);
  background: var(--ai-bg-primary);
  padding: 2px 6px;
  border-radius: 4px;
}

/* 分析结果 */
.ai-analysis-result {
  padding: 16px;
}

.ai-result-section {
  margin-bottom: 24px;
  border: 1px solid var(--ai-border-color);
  border-radius: 8px;
  overflow: hidden;
}

.ai-result-section:last-child {
  margin-bottom: 0;
}

.ai-result-section__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: var(--ai-bg-secondary);
  border-bottom: 1px solid var(--ai-border-color);
}

.ai-result-section__title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--ai-text-primary);
}

.ai-result-section__icon {
  font-size: 16px;
}

.ai-result-section__score {
  display: flex;
  align-items: center;
}

.ai-score {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 20px;
  font-size: 12px;
  font-weight: 600;
  color: white;
  border-radius: 10px;
  background: linear-gradient(135deg, #10b981, #059669);
}

.ai-score[data-score="low"] {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.ai-score[data-score="medium"] {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.ai-result-section__content {
  padding: 16px;
}

/* 内容摘要 */
.ai-summary {
  font-size: 13px;
  line-height: 1.5;
  color: var(--ai-text-primary);
  margin-bottom: 16px;
}

.ai-keywords {
  margin-top: 16px;
}

.ai-keywords__title {
  font-size: 12px;
  font-weight: 600;
  color: var(--ai-text-secondary);
  margin-bottom: 8px;
}

.ai-keywords__list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.ai-keyword-tag {
  display: inline-block;
  padding: 4px 8px;
  font-size: 11px;
  background: var(--ai-accent-color);
  color: white;
  border-radius: 12px;
  font-weight: 500;
}

/* 结构指标 */
.ai-structure-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 12px;
}

.ai-metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: var(--ai-bg-primary);
  border-radius: 6px;
  border: 1px solid var(--ai-border-color);
}

.ai-metric-label {
  font-size: 12px;
  color: var(--ai-text-secondary);
}

.ai-metric-value {
  font-size: 12px;
  font-weight: 600;
  color: var(--ai-text-primary);
}

/* SEO项目 */
.ai-seo-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.ai-seo-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: var(--ai-bg-primary);
  border-radius: 6px;
  border: 1px solid var(--ai-border-color);
}

.ai-seo-item__icon {
  font-size: 14px;
}

.ai-seo-item--pass .ai-seo-item__icon {
  color: #10b981;
}

.ai-seo-item--warning .ai-seo-item__icon {
  color: #f59e0b;
}

.ai-seo-item--fail .ai-seo-item__icon {
  color: #ef4444;
}

.ai-seo-item__text {
  flex: 1;
  font-size: 12px;
  color: var(--ai-text-primary);
}

/* 智能建议 */
.ai-suggestions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.ai-suggestion-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: var(--ai-bg-primary);
  border: 1px solid var(--ai-border-color);
  border-radius: 8px;
}

.ai-suggestion-item__icon {
  font-size: 16px;
  flex-shrink: 0;
  margin-top: 2px;
}

.ai-suggestion-item__content {
  flex: 1;
}

.ai-suggestion-item__title {
  font-size: 13px;
  font-weight: 600;
  color: var(--ai-text-primary);
  margin: 0 0 4px 0;
}

.ai-suggestion-item__desc {
  font-size: 12px;
  color: var(--ai-text-secondary);
  line-height: 1.4;
}

.ai-suggestion-item__priority {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
  font-weight: 500;
  align-self: flex-start;
  margin-top: 2px;
}

.ai-suggestion-item__priority--high {
  background: #fef2f2;
  color: #dc2626;
}

.ai-suggestion-item__priority--medium {
  background: #fffbeb;
  color: #d97706;
}

.ai-suggestion-item__priority--low {
  background: #f0fdf4;
  color: #059669;
}
/* #endregion */

/* #region 增强面板样式 */
/* 模板库样式 */
.ai-templates__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid var(--ai-border-color);
}

.ai-templates__header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--ai-text-primary);
}

.ai-templates__controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.ai-templates__search {
  padding: 16px;
  border-bottom: 1px solid var(--ai-border-color);
}

.ai-search__input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.ai-search__input {
  flex: 1;
  padding: 8px 40px 8px 12px;
  border: 1px solid var(--ai-border-color);
  border-radius: 6px;
  font-size: 13px;
  background: var(--ai-bg-primary);
  color: var(--ai-text-primary);
}

.ai-search__input:focus {
  outline: none;
  border-color: var(--ai-accent-color);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.ai-search__btn {
  position: absolute;
  right: 8px;
  padding: 4px;
  background: none;
  border: none;
  color: var(--ai-text-secondary);
  cursor: pointer;
  border-radius: 4px;
}

.ai-search__btn:hover {
  background: var(--ai-bg-secondary);
  color: var(--ai-text-primary);
}

/* 模板卡片 */
.ai-templates__presets {
  padding: 16px;
  border-bottom: 1px solid var(--ai-border-color);
}

.ai-templates__list {
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.ai-template-card {
  border: 1px solid var(--ai-border-color);
  border-radius: 8px;
  margin-bottom: 12px;
  background: var(--ai-bg-primary);
  transition: all 0.2s ease;
}

.ai-template-card:last-child {
  margin-bottom: 0;
}

.ai-template-card:hover {
  border-color: var(--ai-accent-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ai-template-card__header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-bottom: 1px solid var(--ai-border-color);
}

.ai-template-card__icon {
  font-size: 18px;
  flex-shrink: 0;
}

.ai-template-card__info {
  flex: 1;
  min-width: 0;
}

.ai-template-card__title {
  margin: 0 0 2px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--ai-text-primary);
}

.ai-template-card__category {
  font-size: 11px;
  color: var(--ai-text-muted);
  background: var(--ai-bg-secondary);
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
}

.ai-template-card__actions {
  display: flex;
  gap: 4px;
}

.ai-template-card__btn {
  padding: 6px;
  background: none;
  border: 1px solid var(--ai-border-color);
  border-radius: 4px;
  color: var(--ai-text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.ai-template-card__btn:hover {
  background: var(--ai-bg-secondary);
  color: var(--ai-text-primary);
  border-color: var(--ai-accent-color);
}

.ai-template-card__preview {
  padding: 12px;
  font-size: 12px;
  line-height: 1.4;
  color: var(--ai-text-secondary);
  background: var(--ai-bg-secondary);
  border-bottom: 1px solid var(--ai-border-color);
}

.ai-template-card__meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  font-size: 10px;
  color: var(--ai-text-muted);
}

.ai-template-meta__item {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 光标增强状态卡片 */
.ai-cursor-enhance {
  padding: 16px;
}

.ai-status-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: var(--ai-bg-secondary);
  border: 1px solid var(--ai-border-color);
  border-radius: 8px;
  margin-bottom: 20px;
}

.ai-status-card__icon {
  font-size: 20px;
  flex-shrink: 0;
}

.ai-status-card__content {
  flex: 1;
}

.ai-status-card__title {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--ai-text-primary);
}

.ai-status-card__text {
  font-size: 12px;
  color: var(--ai-text-secondary);
  margin: 0;
}

/* 设置组 */
.ai-settings-group {
  margin-bottom: 24px;
}

.ai-settings-group:last-child {
  margin-bottom: 0;
}

.ai-settings-group__title {
  font-size: 14px;
  font-weight: 600;
  color: var(--ai-text-primary);
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--ai-border-color);
}

.ai-setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid var(--ai-border-color);
}

.ai-setting-item:last-child {
  border-bottom: none;
}

.ai-setting-item__label {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.ai-setting-item__label > span:first-child {
  font-size: 13px;
  font-weight: 500;
  color: var(--ai-text-primary);
}

.ai-setting-item__value {
  font-size: 12px;
  color: var(--ai-text-secondary);
  min-width: 60px;
  text-align: right;
}

/* 快捷键列表 */
.ai-shortcuts {
  padding: 16px;
}

.ai-shortcuts__title {
  font-size: 14px;
  font-weight: 600;
  color: var(--ai-text-primary);
  margin-bottom: 16px;
}

.ai-shortcut-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.ai-shortcut-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  background: var(--ai-bg-secondary);
  border: 1px solid var(--ai-border-color);
  border-radius: 6px;
}

.ai-shortcut-item__label {
  font-size: 13px;
  color: var(--ai-text-primary);
}

/* 统计卡片 */
.ai-enhance-stats {
  padding: 16px;
}

.ai-stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-bottom: 24px;
}

.ai-stat-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: var(--ai-bg-secondary);
  border: 1px solid var(--ai-border-color);
  border-radius: 8px;
}

.ai-stat-card__icon {
  font-size: 20px;
  flex-shrink: 0;
}

.ai-stat-card__content {
  flex: 1;
}

.ai-stat-card__value {
  font-size: 18px;
  font-weight: 700;
  color: var(--ai-text-primary);
  margin-bottom: 2px;
}

.ai-stat-card__label {
  font-size: 11px;
  color: var(--ai-text-secondary);
}

.ai-stats-chart {
  border-top: 1px solid var(--ai-border-color);
  padding-top: 20px;
}

.ai-stats-chart h4 {
  font-size: 14px;
  font-weight: 600;
  color: var(--ai-text-primary);
  margin-bottom: 16px;
}

.ai-chart-container {
  height: 200px;
  background: var(--ai-bg-secondary);
  border: 1px solid var(--ai-border-color);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}
/* #endregion */

/* #endregion */

/* 通用工具类 */
.hidden {
  display: none !important; /* 使用 !important 确保覆盖其他样式 */
}

/* 进度条初始化样式 */
.ai-progress__fill--init {
  width: 0%;
} 