/**
 * @file 界面重构完成度验证脚本
 * @description 验证对话中心化重构的完成度和质量
 * @version 1.0.0
 */

/**
 * @class RedesignCompletionVerifier
 * @description 重构完成度验证器
 */
class RedesignCompletionVerifier {
  constructor() {
    this.verificationResults = [];
    this.completionScore = 0;
    this.maxScore = 100;
  }

  /**
   * 添加验证结果
   */
  addVerification(category, item, status, score, details) {
    this.verificationResults.push({
      category,
      item,
      status,
      score,
      details,
      timestamp: Date.now()
    });

    if (status === 'COMPLETE') {
      this.completionScore += score;
    } else if (status === 'PARTIAL') {
      this.completionScore += Math.floor(score * 0.5);
    }

    const statusIcon = {
      'COMPLETE': '✅',
      'PARTIAL': '🟡', 
      'MISSING': '❌',
      'ERROR': '🔴'
    }[status] || '❓';

    console.log(`${statusIcon} [${category}] ${item}: ${details}`);
  }

  /**
   * 验证HTML结构重构完成度
   */
  verifyHTMLRestructure() {
    console.log('\n🏗️ 验证HTML结构重构');

    const checks = [
      {
        item: '主容器简化',
        check: () => !!document.getElementById('ai-sidebar-container'),
        score: 10
      },
      {
        item: '精简顶部栏',
        check: () => {
          const header = document.querySelector('.ai-sidebar__header');
          return header && header.children.length <= 3;
        },
        score: 8
      },
      {
        item: '快捷模板系统',
        check: () => {
          const templates = document.querySelectorAll('.ai-template-item');
          return templates.length >= 5;
        },
        score: 12
      },
      {
        item: '对话中心化布局',
        check: () => {
          const chat = document.querySelector('.ai-chat');
          const main = document.querySelector('.ai-sidebar__main');
          return chat && main && main.children.length <= 2;
        },
        score: 15
      },
      {
        item: '悬浮操作菜单',
        check: () => !!document.querySelector('.ai-chat__hover-actions'),
        score: 8
      },
      {
        item: '统一设置入口',
        check: () => {
          const modal = document.getElementById('ai-settings-modal');
          const btn = document.getElementById('ai-sidebar-settings-btn');
          return modal && btn;
        },
        score: 7
      },
      {
        item: '冗余组件移除',
        check: () => {
          const removed = [
            '.ai-analysis__tabs',
            '.ai-notion__tabs',
            '.ai-enhance__tabs',
            '.ai-sidebar__footer'
          ];
          return removed.every(sel => !document.querySelector(sel));
        },
        score: 10
      }
    ];

    checks.forEach(({ item, check, score }) => {
      try {
        const result = check();
        this.addVerification(
          'HTML重构',
          item,
          result ? 'COMPLETE' : 'MISSING',
          score,
          result ? '已完成' : '未完成'
        );
      } catch (error) {
        this.addVerification('HTML重构', item, 'ERROR', 0, error.message);
      }
    });
  }

  /**
   * 验证CSS样式优化完成度
   */
  verifyCSSOptimization() {
    console.log('\n🎨 验证CSS样式优化');

    const checks = [
      {
        item: '响应式布局',
        check: () => {
          const container = document.getElementById('ai-sidebar-container');
          const styles = window.getComputedStyle(container);
          return styles.display === 'flex' && styles.flexDirection === 'column';
        },
        score: 8
      },
      {
        item: '空白区域移除',
        check: () => {
          const header = document.querySelector('.ai-sidebar__header');
          const headerHeight = parseInt(window.getComputedStyle(header).height);
          return headerHeight <= 50;
        },
        score: 6
      },
      {
        item: '对话气泡优化',
        check: () => {
          const bubble = document.querySelector('.ai-chat__bubble');
          return bubble && window.getComputedStyle(bubble).borderRadius !== '0px';
        },
        score: 5
      },
      {
        item: '输入区域紧凑',
        check: () => {
          const inputArea = document.querySelector('.ai-chat__input-area');
          const height = parseInt(window.getComputedStyle(inputArea).height);
          return height <= 100;
        },
        score: 6
      },
      {
        item: '悬浮菜单样式',
        check: () => {
          const actions = document.querySelector('.ai-chat__hover-actions');
          return actions && window.getComputedStyle(actions).position === 'absolute';
        },
        score: 5
      }
    ];

    checks.forEach(({ item, check, score }) => {
      try {
        const result = check();
        this.addVerification(
          'CSS优化',
          item,
          result ? 'COMPLETE' : 'PARTIAL',
          score,
          result ? '已优化' : '需要调整'
        );
      } catch (error) {
        this.addVerification('CSS优化', item, 'ERROR', 0, error.message);
      }
    });
  }

  /**
   * 验证JavaScript功能重构完成度
   */
  verifyJavaScriptRefactor() {
    console.log('\n⚡ 验证JavaScript功能重构');

    const panelInstance = window.aiSidebarPanel;
    
    if (!panelInstance) {
      this.addVerification('JS重构', '面板实例', 'MISSING', 0, '面板实例不存在');
      return;
    }

    const checks = [
      {
        item: '核心状态管理',
        check: () => !!(panelInstance.messageHistory && panelInstance.appState),
        score: 8
      },
      {
        item: '快捷模板系统',
        check: () => Object.keys(panelInstance.templates).length >= 5,
        score: 8
      },
      {
        item: '多风格回复',
        check: () => !!(panelInstance.currentReplyStyle && panelInstance.currentLanguage),
        score: 6
      },
      {
        item: 'Enter键发送',
        check: () => typeof panelInstance.sendMessage === 'function',
        score: 5
      },
      {
        item: '悬浮操作处理',
        check: () => typeof panelInstance.handleBubbleAction === 'function',
        score: 5
      },
      {
        item: '自动页面分析',
        check: () => typeof panelInstance.initializeAutoAnalysis === 'function',
        score: 6
      },
      {
        item: '统一设置管理',
        check: () => !!(panelInstance.openSettingsModal && panelInstance.saveSettings),
        score: 7
      }
    ];

    checks.forEach(({ item, check, score }) => {
      try {
        const result = check();
        this.addVerification(
          'JS重构',
          item,
          result ? 'COMPLETE' : 'MISSING',
          score,
          result ? '功能正常' : '功能缺失'
        );
      } catch (error) {
        this.addVerification('JS重构', item, 'ERROR', 0, error.message);
      }
    });
  }

  /**
   * 验证用户体验改进完成度
   */
  verifyUserExperience() {
    console.log('\n👤 验证用户体验改进');

    const checks = [
      {
        item: '界面简洁性',
        check: () => {
          const totalElements = document.querySelectorAll('*').length;
          const hiddenElements = Array.from(document.querySelectorAll('*'))
            .filter(el => window.getComputedStyle(el).display === 'none').length;
          return hiddenElements / totalElements > 0.2; // 至少20%的元素被隐藏
        },
        score: 5
      },
      {
        item: '对话中心占比',
        check: () => {
          const chat = document.querySelector('.ai-chat');
          const container = document.getElementById('ai-sidebar-container');
          if (!chat || !container) return false;
          return (chat.offsetHeight / container.offsetHeight) > 0.6;
        },
        score: 8
      },
      {
        item: '快捷操作可达性',
        check: () => document.querySelectorAll('.ai-template-item').length >= 5,
        score: 5
      },
      {
        item: '响应式适配',
        check: () => {
          const sidebar = document.getElementById('ai-sidebar-container');
          return sidebar && window.getComputedStyle(sidebar).width !== 'auto';
        },
        score: 5
      },
      {
        item: '无冗余功能',
        check: () => {
          const removedSelectors = [
            '.ai-analysis__tabs',
            '.ai-notion__tabs',
            '.ai-enhance__tabs'
          ];
          return removedSelectors.every(sel => !document.querySelector(sel));
        },
        score: 7
      }
    ];

    checks.forEach(({ item, check, score }) => {
      try {
        const result = check();
        this.addVerification(
          'UX改进',
          item,
          result ? 'COMPLETE' : 'PARTIAL',
          score,
          result ? '体验良好' : '需要改进'
        );
      } catch (error) {
        this.addVerification('UX改进', item, 'ERROR', 0, error.message);
      }
    });
  }

  /**
   * 运行完整验证
   */
  async runCompleteVerification() {
    console.log('🔍 开始界面重构完成度验证');
    console.log('='.repeat(60));

    try {
      // 等待DOM加载
      if (document.readyState !== 'complete') {
        await new Promise(resolve => window.addEventListener('load', resolve));
      }

      // 运行各项验证
      this.verifyHTMLRestructure();
      this.verifyCSSOptimization();
      this.verifyJavaScriptRefactor();
      this.verifyUserExperience();

      // 生成完成度报告
      this.generateCompletionReport();

    } catch (error) {
      console.error('❌ 验证执行失败:', error);
    }
  }

  /**
   * 生成完成度报告
   */
  generateCompletionReport() {
    const completionRate = Math.round((this.completionScore / this.maxScore) * 100);
    
    console.log('\n📋 界面重构完成度报告');
    console.log('='.repeat(60));
    console.log(`总分: ${this.completionScore}/${this.maxScore}`);
    console.log(`完成度: ${completionRate}%`);

    // 按类别统计
    const categories = {};
    this.verificationResults.forEach(result => {
      if (!categories[result.category]) {
        categories[result.category] = { total: 0, complete: 0 };
      }
      categories[result.category].total++;
      if (result.status === 'COMPLETE') {
        categories[result.category].complete++;
      }
    });

    console.log('\n📊 分类完成度:');
    Object.entries(categories).forEach(([category, stats]) => {
      const rate = Math.round((stats.complete / stats.total) * 100);
      console.log(`  ${category}: ${stats.complete}/${stats.total} (${rate}%)`);
    });

    // 未完成项目
    const incomplete = this.verificationResults.filter(r => r.status !== 'COMPLETE');
    if (incomplete.length > 0) {
      console.log('\n⚠️ 未完成项目:');
      incomplete.forEach(item => {
        console.log(`  - [${item.category}] ${item.item}: ${item.details}`);
      });
    }

    // 总体评估
    let assessment;
    if (completionRate >= 95) {
      assessment = '🎉 重构完成度: 优秀 - 可以发布';
    } else if (completionRate >= 85) {
      assessment = '✅ 重构完成度: 良好 - 基本可用';
    } else if (completionRate >= 70) {
      assessment = '⚠️ 重构完成度: 一般 - 需要完善';
    } else {
      assessment = '❌ 重构完成度: 不足 - 需要重大改进';
    }

    console.log(`\n${assessment}`);

    return {
      completionScore: this.completionScore,
      maxScore: this.maxScore,
      completionRate,
      categories,
      incomplete,
      assessment
    };
  }
}

// 全局验证函数
window.verifyRedesignCompletion = async function() {
  const verifier = new RedesignCompletionVerifier();
  return await verifier.runCompleteVerification();
};

console.log('🔍 界面重构完成度验证脚本已加载');
console.log('💡 运行验证: window.verifyRedesignCompletion()');
