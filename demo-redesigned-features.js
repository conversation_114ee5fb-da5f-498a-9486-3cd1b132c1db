/**
 * @file 重构后功能演示脚本
 * @description 演示对话中心化重构后的核心功能
 * @version 1.0.0
 */

/**
 * @class RedesignedFeaturesDemo
 * @description 重构功能演示器
 */
class RedesignedFeaturesDemo {
  constructor() {
    this.demoSteps = [];
    this.currentStep = 0;
    this.panelInstance = window.aiSidebarPanel;
  }

  /**
   * 添加演示步骤
   */
  addDemoStep(name, description, action) {
    this.demoSteps.push({
      name,
      description,
      action,
      completed: false
    });
  }

  /**
   * 初始化演示步骤
   */
  initializeDemoSteps() {
    this.addDemoStep(
      '界面简洁性展示',
      '展示移除冗余组件后的简洁界面',
      () => this.demoInterfaceSimplicity()
    );

    this.addDemoStep(
      '对话中心化体验',
      '演示对话区域占据主要空间的设计',
      () => this.demoChatCentricDesign()
    );

    this.addDemoStep(
      '快捷模板系统',
      '演示一键插入模板功能',
      () => this.demoTemplateSystem()
    );

    this.addDemoStep(
      '多风格回复功能',
      '演示不同风格的AI回复生成',
      () => this.demoMultiStyleReply()
    );

    this.addDemoStep(
      '悬浮操作菜单',
      '演示对话气泡的悬浮操作功能',
      () => this.demoHoverActions()
    );

    this.addDemoStep(
      'Enter键发送',
      '演示优化后的输入体验',
      () => this.demoEnterToSend()
    );

    this.addDemoStep(
      '统一设置入口',
      '演示集成的设置管理功能',
      () => this.demoUnifiedSettings()
    );

    this.addDemoStep(
      '响应式适配',
      '演示不同屏幕尺寸的适配效果',
      () => this.demoResponsiveDesign()
    );
  }

  /**
   * 演示界面简洁性
   */
  async demoInterfaceSimplicity() {
    console.log('🎯 演示: 界面简洁性');
    
    // 统计可见元素
    const allElements = document.querySelectorAll('*');
    const visibleElements = Array.from(allElements)
      .filter(el => window.getComputedStyle(el).display !== 'none');
    
    const simplificationRate = Math.round((1 - visibleElements.length / allElements.length) * 100);
    
    console.log(`✨ 界面简化率: ${simplificationRate}%`);
    console.log(`📊 总元素: ${allElements.length}, 可见元素: ${visibleElements.length}`);
    
    // 高亮主要区域
    this.highlightElement('.ai-sidebar__header', '精简顶部栏');
    await this.wait(1000);
    this.highlightElement('.ai-templates-bar', '快捷模板栏');
    await this.wait(1000);
    this.highlightElement('.ai-chat', '对话中心区域');
    await this.wait(1000);
    this.clearHighlights();
    
    return '界面简洁性演示完成';
  }

  /**
   * 演示对话中心化设计
   */
  async demoChatCentricDesign() {
    console.log('💬 演示: 对话中心化设计');
    
    const chatArea = document.querySelector('.ai-chat');
    const container = document.getElementById('ai-sidebar-container');
    
    if (chatArea && container) {
      const chatHeight = chatArea.offsetHeight;
      const containerHeight = container.offsetHeight;
      const chatRatio = Math.round((chatHeight / containerHeight) * 100);
      
      console.log(`📏 对话区域占比: ${chatRatio}%`);
      
      // 高亮对话区域
      this.highlightElement('.ai-chat', `对话中心区域 (${chatRatio}%)`);
      await this.wait(2000);
      this.clearHighlights();
    }
    
    return '对话中心化设计演示完成';
  }

  /**
   * 演示快捷模板系统
   */
  async demoTemplateSystem() {
    console.log('📝 演示: 快捷模板系统');
    
    const templates = document.querySelectorAll('.ai-template-item');
    console.log(`🎯 可用模板数量: ${templates.length}`);
    
    // 逐个高亮模板
    for (let i = 0; i < Math.min(templates.length, 5); i++) {
      const template = templates[i];
      const templateName = template.querySelector('.ai-template-name')?.textContent || `模板${i+1}`;
      
      this.highlightElement(template, `${templateName}模板`);
      console.log(`✨ 展示模板: ${templateName}`);
      await this.wait(800);
    }
    
    this.clearHighlights();
    return '快捷模板系统演示完成';
  }

  /**
   * 演示多风格回复功能
   */
  async demoMultiStyleReply() {
    console.log('🎨 演示: 多风格回复功能');
    
    const replyStyleSelect = document.getElementById('ai-reply-style');
    const languageSelect = document.getElementById('ai-language');
    
    if (replyStyleSelect && languageSelect) {
      // 高亮选择器
      this.highlightElement(replyStyleSelect, '回复风格选择');
      await this.wait(1000);
      this.highlightElement(languageSelect, '语言选择');
      await this.wait(1000);
      
      // 演示风格切换
      const styles = ['professional', 'friendly', 'detailed', 'concise'];
      for (const style of styles) {
        if (Array.from(replyStyleSelect.options).some(opt => opt.value === style)) {
          replyStyleSelect.value = style;
          replyStyleSelect.dispatchEvent(new Event('change'));
          console.log(`🔄 切换到风格: ${style}`);
          await this.wait(500);
        }
      }
      
      this.clearHighlights();
    }
    
    return '多风格回复功能演示完成';
  }

  /**
   * 演示悬浮操作菜单
   */
  async demoHoverActions() {
    console.log('🎯 演示: 悬浮操作菜单');
    
    const chatBubbles = document.querySelectorAll('.ai-chat__bubble');
    
    if (chatBubbles.length > 0) {
      const firstBubble = chatBubbles[0];
      
      // 模拟悬停效果
      firstBubble.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
      
      const hoverActions = firstBubble.querySelector('.ai-chat__hover-actions');
      if (hoverActions) {
        hoverActions.style.opacity = '1';
        hoverActions.style.visibility = 'visible';
        
        this.highlightElement(hoverActions, '悬浮操作菜单');
        console.log('✨ 悬浮操作菜单已显示');
        
        await this.wait(2000);
        
        // 恢复原状
        hoverActions.style.opacity = '';
        hoverActions.style.visibility = '';
        firstBubble.style.boxShadow = '';
      }
      
      this.clearHighlights();
    }
    
    return '悬浮操作菜单演示完成';
  }

  /**
   * 演示Enter键发送
   */
  async demoEnterToSend() {
    console.log('⌨️ 演示: Enter键发送功能');
    
    const chatInput = document.getElementById('ai-chat-input');
    
    if (chatInput) {
      this.highlightElement(chatInput, 'Enter键发送输入框');
      
      // 模拟输入
      chatInput.focus();
      chatInput.value = '这是一个演示消息';
      
      console.log('✨ 模拟输入: "这是一个演示消息"');
      console.log('💡 按Enter键即可发送 (Shift+Enter换行)');
      
      await this.wait(2000);
      
      // 清空输入
      chatInput.value = '';
      chatInput.blur();
      this.clearHighlights();
    }
    
    return 'Enter键发送功能演示完成';
  }

  /**
   * 演示统一设置入口
   */
  async demoUnifiedSettings() {
    console.log('⚙️ 演示: 统一设置入口');
    
    const settingsBtn = document.getElementById('ai-sidebar-settings-btn');
    
    if (settingsBtn) {
      this.highlightElement(settingsBtn, '统一设置按钮');
      console.log('✨ 所有设置功能已集成到此按钮');
      
      await this.wait(2000);
      this.clearHighlights();
    }
    
    return '统一设置入口演示完成';
  }

  /**
   * 演示响应式适配
   */
  async demoResponsiveDesign() {
    console.log('📱 演示: 响应式适配');
    
    const sidebar = document.getElementById('ai-sidebar-container');
    const currentWidth = window.innerWidth;
    
    console.log(`📏 当前窗口宽度: ${currentWidth}px`);
    
    if (sidebar) {
      const sidebarWidth = parseInt(window.getComputedStyle(sidebar).width);
      console.log(`📐 侧边栏宽度: ${sidebarWidth}px`);
      
      if (currentWidth <= 768) {
        console.log('📱 移动端适配: 侧边栏全屏显示');
      } else if (currentWidth >= 1200) {
        console.log('🖥️ 大屏适配: 侧边栏宽度优化');
      } else {
        console.log('💻 桌面端适配: 标准宽度显示');
      }
      
      this.highlightElement(sidebar, '响应式侧边栏');
      await this.wait(2000);
      this.clearHighlights();
    }
    
    return '响应式适配演示完成';
  }

  /**
   * 高亮元素
   */
  highlightElement(selector, label) {
    const element = typeof selector === 'string' ? document.querySelector(selector) : selector;
    if (!element) return;
    
    element.style.outline = '3px solid #2563eb';
    element.style.outlineOffset = '2px';
    element.style.position = 'relative';
    element.style.zIndex = '999999';
    
    // 添加标签
    const labelEl = document.createElement('div');
    labelEl.className = 'demo-highlight-label';
    labelEl.textContent = label;
    labelEl.style.cssText = `
      position: absolute;
      top: -30px;
      left: 0;
      background: #2563eb;
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: bold;
      z-index: 1000000;
      white-space: nowrap;
    `;
    element.appendChild(labelEl);
  }

  /**
   * 清除所有高亮
   */
  clearHighlights() {
    document.querySelectorAll('[style*="outline"]').forEach(el => {
      el.style.outline = '';
      el.style.outlineOffset = '';
      el.style.position = '';
      el.style.zIndex = '';
    });
    
    document.querySelectorAll('.demo-highlight-label').forEach(el => {
      el.remove();
    });
  }

  /**
   * 等待指定时间
   */
  wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 运行完整演示
   */
  async runCompleteDemo() {
    console.log('🎬 开始重构功能演示');
    console.log('='.repeat(50));
    
    this.initializeDemoSteps();
    
    for (let i = 0; i < this.demoSteps.length; i++) {
      const step = this.demoSteps[i];
      console.log(`\n📍 步骤 ${i + 1}/${this.demoSteps.length}: ${step.name}`);
      console.log(`📝 ${step.description}`);
      
      try {
        const result = await step.action();
        step.completed = true;
        console.log(`✅ ${result}`);
      } catch (error) {
        console.error(`❌ 演示失败: ${error.message}`);
      }
      
      await this.wait(1000);
    }
    
    console.log('\n🎉 重构功能演示完成!');
    console.log(`✅ 完成步骤: ${this.demoSteps.filter(s => s.completed).length}/${this.demoSteps.length}`);
    
    return this.demoSteps;
  }
}

// 全局演示函数
window.demoRedesignedFeatures = async function() {
  const demo = new RedesignedFeaturesDemo();
  return await demo.runCompleteDemo();
};

console.log('🎬 重构功能演示脚本已加载');
console.log('💡 运行演示: window.demoRedesignedFeatures()');
