/**
 * @file 综合功能测试脚本
 * @description 测试所有已实现的性能优化和功能增强
 */

/**
 * @function testComprehensiveFeatures - 综合功能测试
 * @description 测试所有已实现的功能和性能优化
 */
async function testComprehensiveFeatures() {
  console.log('🧪 开始综合功能测试...');
  
  const testResults = {
    performanceOptimizations: {
      memoryManager: { tested: false, passed: false, error: null },
      apiOptimizer: { tested: false, passed: false, error: null },
      moduleLoader: { tested: false, passed: false, error: null },
      uiOptimizer: { tested: false, passed: false, error: null }
    },
    featureEnhancements: {
      contentSummarizer: { tested: false, passed: false, error: null },
      workflowEngine: { tested: false, passed: false, error: null },
      i18nManager: { tested: false, passed: false, error: null },
      collaborationManager: { tested: false, passed: false, error: null }
    },
    integration: {
      performanceIntegrator: { tested: false, passed: false, error: null },
      messageHandling: { tested: false, passed: false, error: null },
      errorRecovery: { tested: false, passed: false, error: null }
    },
    overall: false
  };
  
  try {
    // 测试性能优化模块
    console.log('🔧 测试性能优化模块...');
    await testPerformanceOptimizations(testResults.performanceOptimizations);
    
    // 测试功能增强模块
    console.log('🎯 测试功能增强模块...');
    await testFeatureEnhancements(testResults.featureEnhancements);
    
    // 测试集成功能
    console.log('🔗 测试集成功能...');
    await testIntegrationFeatures(testResults.integration);
    
    // 计算总体结果
    testResults.overall = calculateOverallResult(testResults);
    
    // 输出测试结果
    printComprehensiveTestResults(testResults);
    
  } catch (error) {
    console.error('❌ 综合测试过程中发生错误:', error);
  }
  
  return testResults;
}

/**
 * @function testPerformanceOptimizations - 测试性能优化
 * @description 测试所有性能优化模块
 * @param {Object} results - 测试结果对象
 */
async function testPerformanceOptimizations(results) {
  // 测试内存管理器
  try {
    results.memoryManager.tested = true;
    
    const response = await chrome.runtime.sendMessage({
      type: 'ai:memory:status',
      data: { action: 'check' }
    });
    
    if (response && response.success) {
      results.memoryManager.passed = true;
      console.log('  ✅ 内存管理器测试通过');
    } else {
      results.memoryManager.error = response?.error || '内存管理器响应异常';
    }
  } catch (error) {
    results.memoryManager.error = error.message;
    console.error('  ❌ 内存管理器测试失败:', error);
  }
  
  // 测试API优化器
  try {
    results.apiOptimizer.tested = true;
    
    const response = await chrome.runtime.sendMessage({
      type: 'ai:api:stats',
      data: { action: 'get_stats' }
    });
    
    if (response && response.success) {
      results.apiOptimizer.passed = true;
      console.log('  ✅ API优化器测试通过');
    } else {
      results.apiOptimizer.error = response?.error || 'API优化器响应异常';
    }
  } catch (error) {
    results.apiOptimizer.error = error.message;
    console.error('  ❌ API优化器测试失败:', error);
  }
  
  // 测试模块加载器
  try {
    results.moduleLoader.tested = true;
    
    const response = await chrome.runtime.sendMessage({
      type: 'ai:module:status',
      data: { action: 'get_status' }
    });
    
    if (response && response.success) {
      results.moduleLoader.passed = true;
      console.log('  ✅ 模块加载器测试通过');
    } else {
      results.moduleLoader.error = response?.error || '模块加载器响应异常';
    }
  } catch (error) {
    results.moduleLoader.error = error.message;
    console.error('  ❌ 模块加载器测试失败:', error);
  }
  
  // 测试UI优化器
  try {
    results.uiOptimizer.tested = true;
    
    const response = await chrome.runtime.sendMessage({
      type: 'ai:ui:performance',
      data: { action: 'get_metrics' }
    });
    
    if (response && response.success) {
      results.uiOptimizer.passed = true;
      console.log('  ✅ UI优化器测试通过');
    } else {
      results.uiOptimizer.error = response?.error || 'UI优化器响应异常';
    }
  } catch (error) {
    results.uiOptimizer.error = error.message;
    console.error('  ❌ UI优化器测试失败:', error);
  }
}

/**
 * @function testFeatureEnhancements - 测试功能增强
 * @description 测试所有功能增强模块
 * @param {Object} results - 测试结果对象
 */
async function testFeatureEnhancements(results) {
  // 测试内容摘要器
  try {
    results.contentSummarizer.tested = true;
    
    const response = await chrome.runtime.sendMessage({
      type: 'ai:content:summarize',
      data: { 
        content: '这是一个测试内容，用于验证内容摘要功能是否正常工作。',
        level: 'brief'
      }
    });
    
    if (response && response.success) {
      results.contentSummarizer.passed = true;
      console.log('  ✅ 内容摘要器测试通过');
    } else {
      results.contentSummarizer.error = response?.error || '内容摘要器响应异常';
    }
  } catch (error) {
    results.contentSummarizer.error = error.message;
    console.error('  ❌ 内容摘要器测试失败:', error);
  }
  
  // 测试工作流引擎
  try {
    results.workflowEngine.tested = true;
    
    const response = await chrome.runtime.sendMessage({
      type: 'ai:workflow:list',
      data: { action: 'get_workflows' }
    });
    
    if (response && response.success) {
      results.workflowEngine.passed = true;
      console.log('  ✅ 工作流引擎测试通过');
    } else {
      results.workflowEngine.error = response?.error || '工作流引擎响应异常';
    }
  } catch (error) {
    results.workflowEngine.error = error.message;
    console.error('  ❌ 工作流引擎测试失败:', error);
  }
  
  // 测试国际化管理器
  try {
    results.i18nManager.tested = true;
    
    const response = await chrome.runtime.sendMessage({
      type: 'ai:i18n:get-languages',
      data: { action: 'get_supported_languages' }
    });
    
    if (response && response.success) {
      results.i18nManager.passed = true;
      console.log('  ✅ 国际化管理器测试通过');
    } else {
      results.i18nManager.error = response?.error || '国际化管理器响应异常';
    }
  } catch (error) {
    results.i18nManager.error = error.message;
    console.error('  ❌ 国际化管理器测试失败:', error);
  }
  
  // 测试协作管理器
  try {
    results.collaborationManager.tested = true;
    
    const response = await chrome.runtime.sendMessage({
      type: 'ai:collaboration:create',
      data: { 
        name: '测试工作区',
        description: '用于测试的工作区'
      }
    });
    
    if (response && response.success) {
      results.collaborationManager.passed = true;
      console.log('  ✅ 协作管理器测试通过');
    } else {
      results.collaborationManager.error = response?.error || '协作管理器响应异常';
    }
  } catch (error) {
    results.collaborationManager.error = error.message;
    console.error('  ❌ 协作管理器测试失败:', error);
  }
}

/**
 * @function testIntegrationFeatures - 测试集成功能
 * @description 测试集成和错误处理功能
 * @param {Object} results - 测试结果对象
 */
async function testIntegrationFeatures(results) {
  // 测试性能集成器
  try {
    results.performanceIntegrator.tested = true;
    
    // 检查是否有性能集成器的迹象
    const hasPerformanceFeatures = 
      document.querySelector('.ai-loading-indicator') !== null ||
      document.querySelector('.ai-virtual-scroll-container') !== null ||
      typeof window.testChromeExtensionFixes === 'function';
    
    if (hasPerformanceFeatures) {
      results.performanceIntegrator.passed = true;
      console.log('  ✅ 性能集成器测试通过');
    } else {
      results.performanceIntegrator.error = '未检测到性能集成器功能';
    }
  } catch (error) {
    results.performanceIntegrator.error = error.message;
    console.error('  ❌ 性能集成器测试失败:', error);
  }
  
  // 测试消息处理
  try {
    results.messageHandling.tested = true;
    
    const messageTypes = [
      'ai:memory:status',
      'ai:api:stats',
      'ai:module:status',
      'ai:ui:performance',
      'ai:content:summarize',
      'ai:workflow:list',
      'ai:i18n:get-languages',
      'ai:collaboration:create'
    ];
    
    let successCount = 0;
    
    for (const messageType of messageTypes) {
      try {
        const response = await chrome.runtime.sendMessage({
          type: messageType,
          data: { action: 'test' }
        });
        
        if (response !== undefined) {
          successCount++;
        }
      } catch (error) {
        // 忽略单个消息的错误
      }
    }
    
    if (successCount >= messageTypes.length * 0.7) {
      results.messageHandling.passed = true;
      console.log(`  ✅ 消息处理测试通过 (${successCount}/${messageTypes.length})`);
    } else {
      results.messageHandling.error = `消息处理成功率过低: ${successCount}/${messageTypes.length}`;
    }
  } catch (error) {
    results.messageHandling.error = error.message;
    console.error('  ❌ 消息处理测试失败:', error);
  }
  
  // 测试错误恢复
  try {
    results.errorRecovery.tested = true;
    
    // 测试无效消息类型的处理
    const response = await chrome.runtime.sendMessage({
      type: 'ai:invalid:message:type',
      data: { action: 'test' }
    });
    
    // 应该收到错误响应而不是抛出异常
    if (response && response.success === false && response.error) {
      results.errorRecovery.passed = true;
      console.log('  ✅ 错误恢复测试通过');
    } else {
      results.errorRecovery.error = '错误恢复机制未正常工作';
    }
  } catch (error) {
    results.errorRecovery.error = error.message;
    console.error('  ❌ 错误恢复测试失败:', error);
  }
}

/**
 * @function calculateOverallResult - 计算总体结果
 * @description 计算所有测试的总体结果
 * @param {Object} testResults - 测试结果对象
 * @returns {boolean} 总体是否通过
 */
function calculateOverallResult(testResults) {
  let totalTests = 0;
  let passedTests = 0;
  
  // 统计性能优化模块
  for (const result of Object.values(testResults.performanceOptimizations)) {
    if (result.tested) {
      totalTests++;
      if (result.passed) passedTests++;
    }
  }
  
  // 统计功能增强模块
  for (const result of Object.values(testResults.featureEnhancements)) {
    if (result.tested) {
      totalTests++;
      if (result.passed) passedTests++;
    }
  }
  
  // 统计集成功能
  for (const result of Object.values(testResults.integration)) {
    if (result.tested) {
      totalTests++;
      if (result.passed) passedTests++;
    }
  }
  
  // 至少80%的测试通过才算总体通过
  return totalTests > 0 && (passedTests / totalTests) >= 0.8;
}

/**
 * @function printComprehensiveTestResults - 输出综合测试结果
 * @description 输出详细的测试结果报告
 * @param {Object} testResults - 测试结果对象
 */
function printComprehensiveTestResults(testResults) {
  console.log('\n📊 AI侧边栏综合功能测试结果:');
  console.log('==========================================');
  
  console.log('\n🔧 性能优化模块:');
  printModuleResults(testResults.performanceOptimizations);
  
  console.log('\n🎯 功能增强模块:');
  printModuleResults(testResults.featureEnhancements);
  
  console.log('\n🔗 集成功能:');
  printModuleResults(testResults.integration);
  
  console.log(`\n总体结果: ${testResults.overall ? '🎉 测试通过' : '⚠️ 需要改进'}`);
  
  if (testResults.overall) {
    console.log('\n💡 已实现的功能:');
    console.log('性能优化:');
    console.log('  ✅ 内存管理和自动清理');
    console.log('  ✅ API调用优化和缓存');
    console.log('  ✅ 模块懒加载机制');
    console.log('  ✅ UI渲染性能优化');
    
    console.log('功能增强:');
    console.log('  ✅ 智能内容摘要');
    console.log('  ✅ AI工作流自动化');
    console.log('  ✅ 多语言国际化支持');
    console.log('  ✅ 团队协作功能');
    
    console.log('系统集成:');
    console.log('  ✅ 统一性能监控');
    console.log('  ✅ 完善的消息处理');
    console.log('  ✅ 错误恢复机制');
    
    console.log('\n🚀 AI侧边栏性能优化和功能增强全面完成！');
  } else {
    console.log('\n🔧 需要检查的问题:');
    printFailedTests(testResults);
  }
}

/**
 * @function printModuleResults - 输出模块测试结果
 * @description 输出指定模块组的测试结果
 * @param {Object} moduleResults - 模块测试结果
 */
function printModuleResults(moduleResults) {
  for (const [moduleName, result] of Object.entries(moduleResults)) {
    if (result.tested) {
      const status = result.passed ? '✅ 通过' : '❌ 失败';
      console.log(`  ${moduleName}: ${status}`);
      if (result.error) {
        console.log(`    错误: ${result.error}`);
      }
    } else {
      console.log(`  ${moduleName}: ⏭️ 未测试`);
    }
  }
}

/**
 * @function printFailedTests - 输出失败的测试
 * @description 输出所有失败的测试项目
 * @param {Object} testResults - 测试结果对象
 */
function printFailedTests(testResults) {
  const allResults = {
    ...testResults.performanceOptimizations,
    ...testResults.featureEnhancements,
    ...testResults.integration
  };
  
  for (const [moduleName, result] of Object.entries(allResults)) {
    if (result.tested && !result.passed) {
      console.log(`- ${moduleName}: ${result.error || '未知错误'}`);
    }
  }
}

/**
 * @function quickFeatureCheck - 快速功能检查
 * @description 快速检查主要功能是否正常
 */
async function quickFeatureCheck() {
  console.log('⚡ 快速功能检查...');
  
  try {
    // 检查消息处理
    const response = await chrome.runtime.sendMessage({
      type: 'ai:memory:status',
      data: { action: 'quick_check' }
    });
    
    if (response !== undefined) {
      console.log('✅ 基础消息处理正常');
      console.log('💡 运行 testComprehensiveFeatures() 进行完整测试');
      return true;
    } else {
      console.error('❌ 基础消息处理异常');
      return false;
    }
  } catch (error) {
    console.error('❌ 快速检查异常:', error);
    return false;
  }
}

// 导出函数供控制台调用
window.testComprehensiveFeatures = testComprehensiveFeatures;
window.quickFeatureCheck = quickFeatureCheck;

// 自动提示
if (typeof chrome !== 'undefined' && chrome.runtime) {
  console.log('🎯 AI侧边栏综合功能测试脚本已加载');
  console.log('💡 运行 testComprehensiveFeatures() 进行完整功能测试');
  console.log('💡 运行 quickFeatureCheck() 进行快速检查');
  console.log('📋 测试范围:');
  console.log('  🔧 性能优化: 内存管理、API优化、模块加载、UI优化');
  console.log('  🎯 功能增强: 内容摘要、工作流、国际化、协作');
  console.log('  🔗 系统集成: 性能监控、消息处理、错误恢复');
}
