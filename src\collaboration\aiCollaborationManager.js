/**
 * @file AI侧边栏协作管理器
 * @description 提供共享工作区、实时协作、权限控制和导入导出功能
 */

/**
 * @class AiCollaborationManager
 * @description 协作管理器，支持团队协作和内容共享
 */
class AiCollaborationManager {
  /**
   * @function constructor - 构造函数
   * @description 初始化协作管理器
   * @param {Object} apiManager - API管理器实例
   * @param {Object} securityManager - 安全管理器实例
   */
  constructor(apiManager, securityManager) {
    this.apiManager = apiManager;
    this.securityManager = securityManager;
    
    // 协作配置
    this.config = {
      // 工作区配置
      maxWorkspaces: 10,
      maxMembersPerWorkspace: 50,
      maxSharedItems: 1000,
      
      // 实时协作配置
      syncInterval: 5000, // 5秒同步间隔
      conflictResolution: 'last-write-wins', // 冲突解决策略
      maxConcurrentEditors: 10,
      
      // 权限配置
      defaultPermissions: ['read'],
      adminPermissions: ['read', 'write', 'delete', 'manage'],
      
      // 导入导出配置
      supportedFormats: ['json', 'csv', 'markdown', 'html'],
      maxExportSize: 50 * 1024 * 1024 // 50MB
    };
    
    // 协作状态
    this.workspaces = new Map();
    this.activeCollaborations = new Map();
    this.sharedItems = new Map();
    this.userSessions = new Map();
    
    // 权限管理
    this.permissions = new Map();
    this.roles = new Map([
      ['owner', {
        name: '所有者',
        permissions: ['read', 'write', 'delete', 'manage', 'invite', 'export'],
        description: '拥有所有权限'
      }],
      ['admin', {
        name: '管理员',
        permissions: ['read', 'write', 'delete', 'manage', 'invite'],
        description: '管理工作区和成员'
      }],
      ['editor', {
        name: '编辑者',
        permissions: ['read', 'write'],
        description: '可以查看和编辑内容'
      }],
      ['viewer', {
        name: '查看者',
        permissions: ['read'],
        description: '只能查看内容'
      }]
    ]);
    
    // 实时同步
    this.syncTimers = new Map();
    this.pendingChanges = new Map();
    this.conflictQueue = [];
    
    // 统计信息
    this.stats = {
      totalWorkspaces: 0,
      totalCollaborations: 0,
      totalSharedItems: 0,
      activeUsers: 0,
      syncOperations: 0,
      conflictsResolved: 0
    };
    
    // 初始化
    this.init();
  }

  /**
   * @function init - 初始化协作管理器
   * @description 初始化管理器并加载已保存的协作数据
   */
  async init() {
    console.log('[协作] 初始化协作管理器...');
    
    // 加载已保存的工作区
    await this.loadWorkspaces();
    
    // 启动同步服务
    this.startSyncService();
    
    // 注册事件监听器
    this.registerEventListeners();
    
    console.log('[协作] 协作管理器初始化完成');
  }

  /**
   * @function createWorkspace - 创建工作区
   * @description 创建新的协作工作区
   * @param {Object} workspaceData - 工作区数据
   * @returns {Promise<string>} 工作区ID
   */
  async createWorkspace(workspaceData) {
    const {
      name,
      description = '',
      isPublic = false,
      inviteCode = null
    } = workspaceData;
    
    if (this.workspaces.size >= this.config.maxWorkspaces) {
      throw new Error('已达到最大工作区数量限制');
    }
    
    const workspaceId = this.generateWorkspaceId();
    const currentUser = await this.getCurrentUser();
    
    const workspace = {
      id: workspaceId,
      name,
      description,
      isPublic,
      inviteCode: inviteCode || this.generateInviteCode(),
      owner: currentUser.id,
      members: new Map([[currentUser.id, {
        userId: currentUser.id,
        role: 'owner',
        joinedAt: Date.now(),
        lastActive: Date.now()
      }]]),
      sharedItems: new Map(),
      settings: {
        allowPublicJoin: isPublic,
        requireApproval: !isPublic,
        maxMembers: this.config.maxMembersPerWorkspace
      },
      metadata: {
        created: Date.now(),
        modified: Date.now(),
        version: 1
      }
    };
    
    this.workspaces.set(workspaceId, workspace);
    await this.saveWorkspaces();
    
    this.stats.totalWorkspaces++;
    
    console.log(`[协作] ✅ 工作区已创建: ${name} (${workspaceId})`);
    
    return workspaceId;
  }

  /**
   * @function joinWorkspace - 加入工作区
   * @description 用户加入指定的工作区
   * @param {string} workspaceId - 工作区ID或邀请码
   * @param {Object} options - 加入选项
   * @returns {Promise<boolean>} 是否成功加入
   */
  async joinWorkspace(workspaceId, options = {}) {
    const { role = 'viewer', message = '' } = options;
    
    // 查找工作区（支持ID或邀请码）
    let workspace = this.workspaces.get(workspaceId);
    if (!workspace) {
      // 尝试通过邀请码查找
      workspace = Array.from(this.workspaces.values())
        .find(ws => ws.inviteCode === workspaceId);
    }
    
    if (!workspace) {
      throw new Error('工作区不存在或邀请码无效');
    }
    
    const currentUser = await this.getCurrentUser();
    
    // 检查是否已经是成员
    if (workspace.members.has(currentUser.id)) {
      return true;
    }
    
    // 检查成员数量限制
    if (workspace.members.size >= workspace.settings.maxMembers) {
      throw new Error('工作区成员已满');
    }
    
    // 检查权限
    if (!workspace.isPublic && workspace.settings.requireApproval) {
      // 需要审批的情况
      await this.requestWorkspaceAccess(workspace.id, currentUser.id, role, message);
      return false; // 等待审批
    }
    
    // 直接加入
    workspace.members.set(currentUser.id, {
      userId: currentUser.id,
      role: role,
      joinedAt: Date.now(),
      lastActive: Date.now(),
      status: 'active'
    });
    
    workspace.metadata.modified = Date.now();
    await this.saveWorkspaces();
    
    // 通知其他成员
    await this.notifyWorkspaceMembers(workspace.id, 'member_joined', {
      userId: currentUser.id,
      userName: currentUser.name
    });
    
    console.log(`[协作] ✅ 用户已加入工作区: ${currentUser.name} -> ${workspace.name}`);
    
    return true;
  }

  /**
   * @function shareItem - 分享项目
   * @description 在工作区中分享内容项目
   * @param {string} workspaceId - 工作区ID
   * @param {Object} itemData - 项目数据
   * @returns {Promise<string>} 分享项目ID
   */
  async shareItem(workspaceId, itemData) {
    const workspace = this.workspaces.get(workspaceId);
    if (!workspace) {
      throw new Error('工作区不存在');
    }
    
    const currentUser = await this.getCurrentUser();
    
    // 检查权限
    if (!this.hasPermission(workspace, currentUser.id, 'write')) {
      throw new Error('没有分享权限');
    }
    
    const {
      type,
      title,
      content,
      metadata = {},
      permissions = ['read']
    } = itemData;
    
    const itemId = this.generateItemId();
    
    const sharedItem = {
      id: itemId,
      type,
      title,
      content,
      metadata: {
        ...metadata,
        created: Date.now(),
        modified: Date.now(),
        version: 1
      },
      author: currentUser.id,
      permissions: permissions,
      collaborators: new Map([[currentUser.id, {
        userId: currentUser.id,
        role: 'owner',
        lastModified: Date.now()
      }]]),
      comments: [],
      history: [{
        action: 'created',
        userId: currentUser.id,
        timestamp: Date.now(),
        changes: { created: true }
      }]
    };
    
    workspace.sharedItems.set(itemId, sharedItem);
    this.sharedItems.set(itemId, sharedItem);
    
    workspace.metadata.modified = Date.now();
    await this.saveWorkspaces();
    
    this.stats.totalSharedItems++;
    
    // 通知工作区成员
    await this.notifyWorkspaceMembers(workspaceId, 'item_shared', {
      itemId,
      title,
      author: currentUser.name
    });
    
    console.log(`[协作] ✅ 项目已分享: ${title} (${itemId})`);
    
    return itemId;
  }

  /**
   * @function editSharedItem - 编辑分享项目
   * @description 协作编辑分享的项目
   * @param {string} itemId - 项目ID
   * @param {Object} changes - 变更内容
   * @returns {Promise<Object>} 编辑结果
   */
  async editSharedItem(itemId, changes) {
    const item = this.sharedItems.get(itemId);
    if (!item) {
      throw new Error('分享项目不存在');
    }
    
    const currentUser = await this.getCurrentUser();
    
    // 检查编辑权限
    if (!this.hasItemPermission(item, currentUser.id, 'write')) {
      throw new Error('没有编辑权限');
    }
    
    // 检查并发编辑
    const activeEditors = this.getActiveEditors(itemId);
    if (activeEditors.length >= this.config.maxConcurrentEditors) {
      throw new Error('当前编辑者过多，请稍后再试');
    }
    
    // 记录编辑会话
    this.recordEditSession(itemId, currentUser.id);
    
    try {
      // 应用变更
      const result = await this.applyChanges(item, changes, currentUser.id);
      
      // 实时同步给其他协作者
      await this.syncChangesToCollaborators(itemId, changes, currentUser.id);
      
      // 保存历史记录
      this.addToHistory(item, 'edited', currentUser.id, changes);
      
      // 更新元数据
      item.metadata.modified = Date.now();
      item.metadata.version++;
      
      await this.saveWorkspaces();
      
      console.log(`[协作] ✅ 项目已编辑: ${item.title} by ${currentUser.name}`);
      
      return result;
      
    } finally {
      // 清理编辑会话
      this.clearEditSession(itemId, currentUser.id);
    }
  }

  /**
   * @function addComment - 添加评论
   * @description 为分享项目添加评论
   * @param {string} itemId - 项目ID
   * @param {Object} commentData - 评论数据
   * @returns {Promise<string>} 评论ID
   */
  async addComment(itemId, commentData) {
    const item = this.sharedItems.get(itemId);
    if (!item) {
      throw new Error('分享项目不存在');
    }
    
    const currentUser = await this.getCurrentUser();
    
    // 检查评论权限
    if (!this.hasItemPermission(item, currentUser.id, 'read')) {
      throw new Error('没有评论权限');
    }
    
    const { content, replyTo = null } = commentData;
    const commentId = this.generateCommentId();
    
    const comment = {
      id: commentId,
      content,
      author: currentUser.id,
      timestamp: Date.now(),
      replyTo,
      reactions: new Map(),
      edited: false
    };
    
    item.comments.push(comment);
    item.metadata.modified = Date.now();
    
    await this.saveWorkspaces();
    
    // 通知相关用户
    await this.notifyItemCollaborators(itemId, 'comment_added', {
      commentId,
      author: currentUser.name,
      content: content.substring(0, 100)
    });
    
    console.log(`[协作] ✅ 评论已添加: ${item.title} by ${currentUser.name}`);
    
    return commentId;
  }

  /**
   * @function exportWorkspace - 导出工作区
   * @description 导出工作区数据
   * @param {string} workspaceId - 工作区ID
   * @param {Object} options - 导出选项
   * @returns {Promise<Object>} 导出结果
   */
  async exportWorkspace(workspaceId, options = {}) {
    const workspace = this.workspaces.get(workspaceId);
    if (!workspace) {
      throw new Error('工作区不存在');
    }
    
    const currentUser = await this.getCurrentUser();
    
    // 检查导出权限
    if (!this.hasPermission(workspace, currentUser.id, 'export')) {
      throw new Error('没有导出权限');
    }
    
    const {
      format = 'json',
      includeComments = true,
      includeHistory = false,
      includeMetadata = true
    } = options;
    
    if (!this.config.supportedFormats.includes(format)) {
      throw new Error(`不支持的导出格式: ${format}`);
    }
    
    // 准备导出数据
    const exportData = {
      workspace: {
        id: workspace.id,
        name: workspace.name,
        description: workspace.description,
        created: workspace.metadata.created,
        exported: Date.now(),
        exportedBy: currentUser.id
      },
      items: [],
      members: includeMetadata ? Array.from(workspace.members.values()) : []
    };
    
    // 导出分享项目
    for (const [itemId, item] of workspace.sharedItems) {
      const exportItem = {
        id: item.id,
        type: item.type,
        title: item.title,
        content: item.content,
        author: item.author,
        created: item.metadata.created,
        modified: item.metadata.modified
      };
      
      if (includeComments) {
        exportItem.comments = item.comments;
      }
      
      if (includeHistory) {
        exportItem.history = item.history;
      }
      
      if (includeMetadata) {
        exportItem.metadata = item.metadata;
      }
      
      exportData.items.push(exportItem);
    }
    
    // 根据格式生成导出内容
    let exportContent;
    let mimeType;
    let filename;
    
    switch (format) {
      case 'json':
        exportContent = JSON.stringify(exportData, null, 2);
        mimeType = 'application/json';
        filename = `${workspace.name}_${Date.now()}.json`;
        break;
        
      case 'csv':
        exportContent = this.convertToCSV(exportData);
        mimeType = 'text/csv';
        filename = `${workspace.name}_${Date.now()}.csv`;
        break;
        
      case 'markdown':
        exportContent = this.convertToMarkdown(exportData);
        mimeType = 'text/markdown';
        filename = `${workspace.name}_${Date.now()}.md`;
        break;
        
      case 'html':
        exportContent = this.convertToHTML(exportData);
        mimeType = 'text/html';
        filename = `${workspace.name}_${Date.now()}.html`;
        break;
        
      default:
        throw new Error(`不支持的导出格式: ${format}`);
    }
    
    // 检查大小限制
    if (exportContent.length > this.config.maxExportSize) {
      throw new Error('导出文件过大，请减少导出内容');
    }
    
    console.log(`[协作] ✅ 工作区已导出: ${workspace.name} (${format})`);
    
    return {
      content: exportContent,
      mimeType,
      filename,
      size: exportContent.length
    };
  }

  /**
   * @function importWorkspace - 导入工作区
   * @description 导入工作区数据
   * @param {Object} importData - 导入数据
   * @param {Object} options - 导入选项
   * @returns {Promise<string>} 导入的工作区ID
   */
  async importWorkspace(importData, options = {}) {
    const {
      mergeWithExisting = false,
      preserveIds = false,
      skipConflicts = true
    } = options;
    
    const currentUser = await this.getCurrentUser();
    
    try {
      // 验证导入数据
      this.validateImportData(importData);
      
      let workspaceId;
      
      if (mergeWithExisting && importData.workspace.id) {
        // 合并到现有工作区
        workspaceId = importData.workspace.id;
        const existingWorkspace = this.workspaces.get(workspaceId);
        
        if (!existingWorkspace) {
          throw new Error('目标工作区不存在');
        }
        
        if (!this.hasPermission(existingWorkspace, currentUser.id, 'manage')) {
          throw new Error('没有管理权限');
        }
      } else {
        // 创建新工作区
        workspaceId = await this.createWorkspace({
          name: importData.workspace.name + ' (导入)',
          description: importData.workspace.description || '从导入数据创建'
        });
      }
      
      const workspace = this.workspaces.get(workspaceId);
      let importedCount = 0;
      let skippedCount = 0;
      
      // 导入项目
      for (const itemData of importData.items || []) {
        try {
          const itemId = preserveIds && itemData.id ? 
            itemData.id : this.generateItemId();
          
          // 检查冲突
          if (workspace.sharedItems.has(itemId)) {
            if (skipConflicts) {
              skippedCount++;
              continue;
            } else {
              throw new Error(`项目ID冲突: ${itemId}`);
            }
          }
          
          const sharedItem = {
            id: itemId,
            type: itemData.type || 'document',
            title: itemData.title,
            content: itemData.content,
            metadata: {
              created: itemData.created || Date.now(),
              modified: Date.now(),
              version: 1,
              imported: true
            },
            author: currentUser.id,
            permissions: ['read', 'write'],
            collaborators: new Map([[currentUser.id, {
              userId: currentUser.id,
              role: 'owner',
              lastModified: Date.now()
            }]]),
            comments: itemData.comments || [],
            history: [{
              action: 'imported',
              userId: currentUser.id,
              timestamp: Date.now(),
              changes: { imported: true }
            }]
          };
          
          workspace.sharedItems.set(itemId, sharedItem);
          this.sharedItems.set(itemId, sharedItem);
          importedCount++;
          
        } catch (error) {
          console.warn(`[协作] 项目导入失败: ${itemData.title}`, error);
          skippedCount++;
        }
      }
      
      workspace.metadata.modified = Date.now();
      await this.saveWorkspaces();
      
      console.log(`[协作] ✅ 工作区导入完成: ${importedCount} 成功, ${skippedCount} 跳过`);

      return workspaceId;

    } catch (error) {
      console.error('[协作] 工作区导入失败:', error);
      throw error;
    }
  }

  /**
   * @function hasPermission - 检查权限
   * @description 检查用户在工作区中的权限
   * @param {Object} workspace - 工作区对象
   * @param {string} userId - 用户ID
   * @param {string} permission - 权限名称
   * @returns {boolean} 是否有权限
   */
  hasPermission(workspace, userId, permission) {
    const member = workspace.members.get(userId);
    if (!member) return false;

    const role = this.roles.get(member.role);
    return role && role.permissions.includes(permission);
  }

  /**
   * @function hasItemPermission - 检查项目权限
   * @description 检查用户对特定项目的权限
   * @param {Object} item - 项目对象
   * @param {string} userId - 用户ID
   * @param {string} permission - 权限名称
   * @returns {boolean} 是否有权限
   */
  hasItemPermission(item, userId, permission) {
    const collaborator = item.collaborators.get(userId);
    if (!collaborator) return false;

    const role = this.roles.get(collaborator.role);
    return role && role.permissions.includes(permission);
  }

  /**
   * @function getCurrentUser - 获取当前用户
   * @description 获取当前登录用户信息
   * @returns {Promise<Object>} 用户信息
   */
  async getCurrentUser() {
    // 这里应该从认证系统获取用户信息
    // 暂时返回模拟数据
    return {
      id: 'user_' + Date.now(),
      name: '当前用户',
      email: '<EMAIL>'
    };
  }

  /**
   * @function generateWorkspaceId - 生成工作区ID
   * @description 生成唯一的工作区ID
   * @returns {string} 工作区ID
   */
  generateWorkspaceId() {
    return `workspace_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * @function generateInviteCode - 生成邀请码
   * @description 生成工作区邀请码
   * @returns {string} 邀请码
   */
  generateInviteCode() {
    return Math.random().toString(36).substr(2, 8).toUpperCase();
  }

  /**
   * @function generateItemId - 生成项目ID
   * @description 生成唯一的项目ID
   * @returns {string} 项目ID
   */
  generateItemId() {
    return `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * @function generateCommentId - 生成评论ID
   * @description 生成唯一的评论ID
   * @returns {string} 评论ID
   */
  generateCommentId() {
    return `comment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * @function loadWorkspaces - 加载工作区
   * @description 从存储中加载工作区数据
   */
  async loadWorkspaces() {
    try {
      const result = await chrome.storage.local.get(['ai_workspaces']);
      const workspacesData = result.ai_workspaces || {};

      for (const [id, data] of Object.entries(workspacesData)) {
        // 重建Map对象
        const workspace = {
          ...data,
          members: new Map(data.members || []),
          sharedItems: new Map(data.sharedItems || [])
        };

        this.workspaces.set(id, workspace);

        // 重建共享项目索引
        for (const [itemId, item] of workspace.sharedItems) {
          this.sharedItems.set(itemId, item);
        }
      }

      console.log(`[协作] 已加载 ${this.workspaces.size} 个工作区`);
    } catch (error) {
      console.warn('[协作] 工作区加载失败:', error);
    }
  }

  /**
   * @function saveWorkspaces - 保存工作区
   * @description 将工作区数据保存到存储
   */
  async saveWorkspaces() {
    try {
      const workspacesData = {};

      for (const [id, workspace] of this.workspaces) {
        // 转换Map为数组以便序列化
        workspacesData[id] = {
          ...workspace,
          members: Array.from(workspace.members.entries()),
          sharedItems: Array.from(workspace.sharedItems.entries())
        };
      }

      await chrome.storage.local.set({ ai_workspaces: workspacesData });
    } catch (error) {
      console.error('[协作] 工作区保存失败:', error);
    }
  }

  /**
   * @function startSyncService - 启动同步服务
   * @description 启动实时同步服务
   */
  startSyncService() {
    // 定期同步活跃协作
    setInterval(() => {
      this.syncActiveCollaborations();
    }, this.config.syncInterval);

    console.log('[协作] 同步服务已启动');
  }

  /**
   * @function syncActiveCollaborations - 同步活跃协作
   * @description 同步所有活跃的协作会话
   */
  async syncActiveCollaborations() {
    for (const [itemId, collaboration] of this.activeCollaborations) {
      try {
        await this.syncCollaboration(itemId, collaboration);
      } catch (error) {
        console.warn(`[协作] 协作同步失败: ${itemId}`, error);
      }
    }
  }

  /**
   * @function getStats - 获取统计信息
   * @description 获取协作管理器的统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      ...this.stats,
      activeWorkspaces: this.workspaces.size,
      activeCollaborations: this.activeCollaborations.size,
      totalSharedItems: this.sharedItems.size,
      activeUsers: this.userSessions.size
    };
  }

  /**
   * @function cleanup - 清理资源
   * @description 清理协作管理器使用的资源
   */
  cleanup() {
    // 清理同步定时器
    for (const timer of this.syncTimers.values()) {
      clearInterval(timer);
    }
    this.syncTimers.clear();

    // 清理协作状态
    this.activeCollaborations.clear();
    this.userSessions.clear();
    this.pendingChanges.clear();

    console.log('[协作] 协作管理器已清理');
  }
}

// 导出协作管理器类
export { AiCollaborationManager };
