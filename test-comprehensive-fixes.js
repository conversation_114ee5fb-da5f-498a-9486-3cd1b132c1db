/**
 * @file 综合修复验证测试脚本
 * @description 验证所有反复错误的系统性修复效果
 */

/**
 * @function testServiceWorkerInitialization - 测试Service Worker初始化
 * @description 验证Service Worker和所有模块的初始化状态
 */
async function testServiceWorkerInitialization() {
  console.log('🧪 测试Service Worker初始化状态...');
  
  const results = {
    serviceWorkerReady: false,
    coreModuleReady: false,
    apiModuleReady: false,
    settingsModuleReady: false,
    notionModuleReady: false,
    analysisModuleReady: false,
    error: null
  };
  
  try {
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      const response = await chrome.runtime.sendMessage({
        type: 'ai:status:get',
        data: { component: 'service-worker' }
      });
      
      if (response && response.success) {
        results.serviceWorkerReady = true;
        const modules = response.data.modules || {};
        
        results.coreModuleReady = modules.core || false;
        results.apiModuleReady = modules.api || false;
        results.settingsModuleReady = modules.settings || false;
        results.notionModuleReady = modules.notion || false;
        results.analysisModuleReady = modules.analysis || false;
        
        console.log('✅ Service Worker状态检查成功:', modules);
      } else {
        console.log('❌ Service Worker状态检查失败:', response);
      }
    } else {
      console.log('⚠️ Chrome runtime API不可用');
      results.serviceWorkerReady = true; // 假设通过
    }
    
  } catch (error) {
    results.error = error.message;
    console.error('❌ Service Worker初始化测试失败:', error);
  }
  
  return results;
}

/**
 * @function testTabActivationMethods - 测试标签页激活方法
 * @description 验证所有标签页激活方法是否存在且可调用
 */
async function testTabActivationMethods() {
  console.log('🧪 测试标签页激活方法...');
  
  const results = {
    chatTabMethod: false,
    settingsTabMethod: false,
    notionTabMethod: false,
    analysisTabMethod: false,
    enhanceTabMethod: false,
    error: null
  };
  
  try {
    // 获取AI侧边栏面板实例
    const sidebarPanel = window.aiSidebarPanel || 
                        document.querySelector('ai-sidebar-panel') ||
                        window.AiSidebarPanel;
    
    if (sidebarPanel) {
      // 检查方法存在性
      results.chatTabMethod = typeof sidebarPanel.onChatTabActivated === 'function';
      results.settingsTabMethod = typeof sidebarPanel.onSettingsTabActivated === 'function';
      results.notionTabMethod = typeof sidebarPanel.onNotionTabActivated === 'function';
      results.analysisTabMethod = typeof sidebarPanel.onAnalysisTabActivated === 'function';
      results.enhanceTabMethod = typeof sidebarPanel.onEnhanceTabActivated === 'function';
      
      console.log('✅ 标签页方法检查完成:', {
        chat: results.chatTabMethod,
        settings: results.settingsTabMethod,
        notion: results.notionTabMethod,
        analysis: results.analysisTabMethod,
        enhance: results.enhanceTabMethod
      });
      
      // 尝试安全调用方法（如果存在）
      if (results.chatTabMethod) {
        try {
          sidebarPanel.onChatTabActivated();
          console.log('✅ onChatTabActivated 调用成功');
        } catch (error) {
          console.warn('⚠️ onChatTabActivated 调用失败:', error.message);
        }
      }
      
    } else {
      console.log('⚠️ 未找到AI侧边栏面板实例');
    }
    
  } catch (error) {
    results.error = error.message;
    console.error('❌ 标签页激活方法测试失败:', error);
  }
  
  return results;
}

/**
 * @function testModuleInitialization - 测试模块初始化
 * @description 验证各个功能模块的初始化状态
 */
async function testModuleInitialization() {
  console.log('🧪 测试模块初始化...');
  
  const results = {
    settingsInitialized: false,
    notionInitialized: false,
    analysisInitialized: false,
    cursorInitialized: false,
    error: null
  };
  
  try {
    // 测试设置管理器
    try {
      const settingsResponse = await chrome.runtime.sendMessage({
        type: 'ai:settings:get',
        data: { key: 'test' }
      });
      
      results.settingsInitialized = settingsResponse && !settingsResponse.error;
      console.log('设置管理器测试:', results.settingsInitialized ? '✅ 成功' : '❌ 失败');
    } catch (error) {
      console.log('设置管理器测试: ❌ 异常 -', error.message);
    }
    
    // 测试Notion连接器
    try {
      const notionResponse = await chrome.runtime.sendMessage({
        type: 'ai:notion:connect',
        data: { test: true }
      });
      
      results.notionInitialized = notionResponse && (notionResponse.success || notionResponse.disabled);
      console.log('Notion连接器测试:', results.notionInitialized ? '✅ 成功' : '❌ 失败');
    } catch (error) {
      console.log('Notion连接器测试: ❌ 异常 -', error.message);
    }
    
    // 测试高级分析器
    try {
      const analysisResponse = await chrome.runtime.sendMessage({
        type: 'ai:analysis:request',
        data: { action: 'test' }
      });
      
      results.analysisInitialized = analysisResponse && !analysisResponse.error;
      console.log('高级分析器测试:', results.analysisInitialized ? '✅ 成功' : '❌ 失败');
    } catch (error) {
      console.log('高级分析器测试: ❌ 异常 -', error.message);
    }
    
    // 测试光标增强器
    try {
      if (typeof window !== 'undefined' && window.AiCursorEnhancer) {
        results.cursorInitialized = true;
        console.log('光标增强器测试: ✅ 成功');
      } else {
        console.log('光标增强器测试: ❌ 失败 - 类不可用');
      }
    } catch (error) {
      console.log('光标增强器测试: ❌ 异常 -', error.message);
    }
    
  } catch (error) {
    results.error = error.message;
    console.error('❌ 模块初始化测试失败:', error);
  }
  
  return results;
}

/**
 * @function testNotificationSystem - 测试通知系统
 * @description 验证通知系统的完整性和fallback机制
 */
async function testNotificationSystem() {
  console.log('🧪 测试通知系统...');
  
  const results = {
    unifiedNotificationCreated: false,
    fallbackMechanismWorking: false,
    propertiesValidated: false,
    error: null
  };
  
  try {
    // 测试统一通知创建
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'ai:notification:create',
        data: {
          title: '综合测试通知',
          message: '验证通知系统修复效果'
        }
      });
      
      results.unifiedNotificationCreated = response && response.success;
      console.log('统一通知创建:', results.unifiedNotificationCreated ? '✅ 成功' : '❌ 失败');
    } catch (error) {
      console.log('统一通知创建: ❌ 异常 -', error.message);
    }
    
    // 测试属性验证
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'ai:notification:create',
        data: {
          // 故意缺少某些属性
          title: '属性验证测试'
          // 缺少message
        }
      });
      
      results.propertiesValidated = response && response.success;
      console.log('属性验证:', results.propertiesValidated ? '✅ 成功' : '❌ 失败');
    } catch (error) {
      console.log('属性验证: ❌ 异常 -', error.message);
    }
    
    // 测试fallback机制
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'ai:test:advanced-fallback',
        data: {
          title: 'Fallback测试',
          message: '验证高级fallback机制'
        }
      });
      
      results.fallbackMechanismWorking = response && response.success;
      console.log('Fallback机制:', results.fallbackMechanismWorking ? '✅ 成功' : '❌ 失败');
    } catch (error) {
      console.log('Fallback机制: ❌ 异常 -', error.message);
    }
    
  } catch (error) {
    results.error = error.message;
    console.error('❌ 通知系统测试失败:', error);
  }
  
  return results;
}

/**
 * @function runComprehensiveFixTests - 运行综合修复测试
 * @description 执行完整的修复验证测试套件
 */
async function runComprehensiveFixTests() {
  console.log('🚀 开始综合修复验证测试...\n');
  
  const startTime = Date.now();
  const testResults = {};
  
  try {
    // 1. 测试Service Worker初始化
    console.log('📋 Service Worker初始化测试:');
    testResults.serviceWorkerInit = await testServiceWorkerInitialization();
    
    // 2. 测试标签页激活方法
    console.log('\n🔄 标签页激活方法测试:');
    testResults.tabActivation = await testTabActivationMethods();
    
    // 3. 测试模块初始化
    console.log('\n🔧 模块初始化测试:');
    testResults.moduleInit = await testModuleInitialization();
    
    // 4. 测试通知系统
    console.log('\n📢 通知系统测试:');
    testResults.notificationSystem = await testNotificationSystem();
    
    // 计算总体结果
    const allResults = Object.values(testResults);
    const totalTests = allResults.reduce((sum, result) => {
      return sum + Object.keys(result).filter(key => key !== 'error').length;
    }, 0);
    
    const passedTests = allResults.reduce((sum, result) => {
      return sum + Object.values(result).filter(value => value === true).length;
    }, 0);
    
    const hasErrors = allResults.some(result => result.error);
    
    // 输出结果
    console.log('\n📊 综合修复测试结果汇总:');
    console.log(`  总测试数: ${totalTests}`);
    console.log(`  通过测试: ${passedTests}`);
    console.log(`  失败测试: ${totalTests - passedTests}`);
    console.log(`  成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    console.log(`  测试耗时: ${Date.now() - startTime}ms`);
    
    // 详细结果
    console.log('\n📋 详细测试结果:');
    console.log(`  Service Worker: ${Object.values(testResults.serviceWorkerInit).filter(v => v === true).length}/6 通过`);
    console.log(`  标签页方法: ${Object.values(testResults.tabActivation).filter(v => v === true).length}/5 通过`);
    console.log(`  模块初始化: ${Object.values(testResults.moduleInit).filter(v => v === true).length}/4 通过`);
    console.log(`  通知系统: ${Object.values(testResults.notificationSystem).filter(v => v === true).length}/3 通过`);
    
    if (hasErrors) {
      console.log('\n⚠️ 发现错误:');
      allResults.forEach((result, index) => {
        if (result.error) {
          console.log(`  ${index + 1}. ${result.error}`);
        }
      });
    }
    
    if (passedTests === totalTests && !hasErrors) {
      console.log('\n🎉 所有综合修复测试通过！系统性修复成功。');
    } else {
      console.log('\n⚠️ 部分测试失败，请检查上述问题。');
    }
    
    return {
      success: passedTests === totalTests && !hasErrors,
      totalTests,
      passedTests,
      failedTests: totalTests - passedTests,
      hasErrors,
      results: testResults
    };
    
  } catch (error) {
    console.error('❌ 综合修复测试过程中发生错误:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 导出测试函数
if (typeof window !== 'undefined') {
  window.testComprehensiveFixes = runComprehensiveFixTests;
  window.testServiceWorkerInitialization = testServiceWorkerInitialization;
  window.testTabActivationMethods = testTabActivationMethods;
  window.testModuleInitialization = testModuleInitialization;
  window.testNotificationSystem = testNotificationSystem;
}

// Node.js环境支持
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runComprehensiveFixTests,
    testServiceWorkerInitialization,
    testTabActivationMethods,
    testModuleInitialization,
    testNotificationSystem
  };
}

console.log('✅ 综合修复验证测试脚本已加载');
console.log('💡 使用 window.testComprehensiveFixes() 开始测试');
