/**
 * @file 界面重构集成测试脚本
 * @description 验证对话中心化重构后的功能完整性和用户体验
 * @version 1.0.0
 */

/**
 * @class UIRedesignIntegrationTester
 * @description 界面重构集成测试类
 */
class UIRedesignIntegrationTester {
  constructor() {
    this.testResults = [];
    this.startTime = Date.now();
  }

  /**
   * 添加测试结果
   */
  addResult(testName, success, message, details = null) {
    this.testResults.push({
      testName,
      success,
      message,
      details,
      timestamp: Date.now()
    });
    
    const status = success ? '✅' : '❌';
    console.log(`${status} ${testName}: ${message}`);
    if (details) {
      console.log(`   详情:`, details);
    }
  }

  /**
   * 测试HTML结构重构
   */
  testHTMLStructure() {
    console.log('\n🏗️ 测试HTML结构重构');

    // 测试主容器
    const container = document.getElementById('ai-sidebar-container');
    this.addResult(
      'HTML-主容器',
      !!container,
      container ? '主容器存在' : '主容器缺失'
    );

    // 测试精简顶部栏
    const header = container?.querySelector('.ai-sidebar__header');
    this.addResult(
      'HTML-精简顶部栏',
      !!header,
      header ? '顶部栏结构正确' : '顶部栏缺失'
    );

    // 测试快捷模板系统
    const templatesBar = container?.querySelector('.ai-templates-bar');
    const templateItems = container?.querySelectorAll('.ai-template-item');
    this.addResult(
      'HTML-快捷模板系统',
      !!(templatesBar && templateItems.length >= 5),
      `模板栏存在，包含${templateItems?.length || 0}个模板`
    );

    // 测试对话区域
    const chatMessages = document.getElementById('ai-chat-messages');
    const chatInput = document.getElementById('ai-chat-input');
    this.addResult(
      'HTML-对话区域',
      !!(chatMessages && chatInput),
      '对话消息容器和输入框存在'
    );

    // 测试多风格回复选择器
    const replyStyleSelect = document.getElementById('ai-reply-style');
    const languageSelect = document.getElementById('ai-language');
    this.addResult(
      'HTML-多风格选择器',
      !!(replyStyleSelect && languageSelect),
      '回复风格和语言选择器存在'
    );

    // 测试统一设置模态框
    const settingsModal = document.getElementById('ai-settings-modal');
    this.addResult(
      'HTML-统一设置模态框',
      !!settingsModal,
      settingsModal ? '设置模态框存在' : '设置模态框缺失'
    );

    // 验证移除的冗余组件
    const removedComponents = [
      '.ai-analysis__tabs',
      '.ai-notion__tabs', 
      '.ai-enhance__tabs',
      '.ai-sidebar__footer'
    ];

    let removedCount = 0;
    removedComponents.forEach(selector => {
      if (!document.querySelector(selector)) {
        removedCount++;
      }
    });

    this.addResult(
      'HTML-冗余组件移除',
      removedCount === removedComponents.length,
      `成功移除${removedCount}/${removedComponents.length}个冗余组件`
    );
  }

  /**
   * 测试CSS样式优化
   */
  testCSSOptimization() {
    console.log('\n🎨 测试CSS样式优化');

    const container = document.getElementById('ai-sidebar-container');
    if (!container) {
      this.addResult('CSS-容器检查', false, '主容器不存在');
      return;
    }

    // 测试响应式布局
    const containerStyles = window.getComputedStyle(container);
    this.addResult(
      'CSS-响应式布局',
      containerStyles.display === 'flex' && containerStyles.flexDirection === 'column',
      '主容器使用flex布局'
    );

    // 测试顶部栏高度优化
    const header = container.querySelector('.ai-sidebar__header');
    if (header) {
      const headerHeight = parseInt(window.getComputedStyle(header).height);
      this.addResult(
        'CSS-顶部栏高度',
        headerHeight <= 50,
        `顶部栏高度: ${headerHeight}px (目标: ≤50px)`
      );
    }

    // 测试对话区域占用空间
    const chatMessages = document.getElementById('ai-chat-messages');
    if (chatMessages) {
      const chatStyles = window.getComputedStyle(chatMessages);
      this.addResult(
        'CSS-对话区域布局',
        chatStyles.flex === '1' || chatStyles.flexGrow === '1',
        '对话区域正确占用主要空间'
      );
    }

    // 测试悬浮操作菜单样式
    const hoverActions = container.querySelector('.ai-chat__hover-actions');
    if (hoverActions) {
      const actionStyles = window.getComputedStyle(hoverActions);
      this.addResult(
        'CSS-悬浮操作菜单',
        actionStyles.position === 'absolute',
        '悬浮操作菜单样式正确'
      );
    }

    // 测试输入区域紧凑设计
    const inputArea = container.querySelector('.ai-chat__input-area');
    if (inputArea) {
      const inputStyles = window.getComputedStyle(inputArea);
      const inputHeight = parseInt(inputStyles.height);
      this.addResult(
        'CSS-输入区域紧凑',
        inputHeight <= 100,
        `输入区域高度: ${inputHeight}px (目标: ≤100px)`
      );
    }
  }

  /**
   * 测试JavaScript交互功能
   */
  async testJavaScriptInteractions() {
    console.log('\n⚡ 测试JavaScript交互功能');

    // 测试侧边栏面板实例
    const panelInstance = window.aiSidebarPanel;
    this.addResult(
      'JS-面板实例',
      !!panelInstance,
      panelInstance ? '侧边栏面板实例存在' : '面板实例缺失'
    );

    if (!panelInstance) return;

    // 测试状态管理
    this.addResult(
      'JS-状态管理',
      !!(panelInstance.messageHistory && panelInstance.templates && panelInstance.appState),
      '核心状态对象存在'
    );

    // 测试快捷模板功能
    this.addResult(
      'JS-快捷模板',
      Object.keys(panelInstance.templates).length >= 5,
      `模板数量: ${Object.keys(panelInstance.templates).length}`
    );

    // 测试输入框功能
    const chatInput = document.getElementById('ai-chat-input');
    if (chatInput) {
      // 模拟输入
      chatInput.value = '测试消息';
      chatInput.dispatchEvent(new Event('input'));
      
      this.addResult(
        'JS-输入框交互',
        chatInput.value === '测试消息',
        '输入框响应正常'
      );
      
      // 清空测试输入
      chatInput.value = '';
    }

    // 测试设置模态框
    const settingsBtn = document.getElementById('ai-sidebar-settings-btn');
    if (settingsBtn && panelInstance.openSettingsModal) {
      this.addResult(
        'JS-设置模态框',
        typeof panelInstance.openSettingsModal === 'function',
        '设置模态框功能存在'
      );
    }

    // 测试消息发送功能
    this.addResult(
      'JS-消息发送',
      typeof panelInstance.sendMessage === 'function',
      '消息发送方法存在'
    );

    // 测试自动页面分析
    this.addResult(
      'JS-自动页面分析',
      typeof panelInstance.initializeAutoAnalysis === 'function',
      '自动页面分析功能存在'
    );
  }

  /**
   * 测试用户体验优化
   */
  testUserExperience() {
    console.log('\n👤 测试用户体验优化');

    // 测试界面简洁性
    const totalElements = document.querySelectorAll('*').length;
    const visibleElements = Array.from(document.querySelectorAll('*'))
      .filter(el => window.getComputedStyle(el).display !== 'none').length;
    
    this.addResult(
      'UX-界面简洁性',
      visibleElements < totalElements * 0.8,
      `可见元素: ${visibleElements}/${totalElements} (简化率: ${Math.round((1 - visibleElements/totalElements) * 100)}%)`
    );

    // 测试对话中心化
    const chatArea = document.querySelector('.ai-chat');
    const container = document.getElementById('ai-sidebar-container');
    
    if (chatArea && container) {
      const chatHeight = chatArea.offsetHeight;
      const containerHeight = container.offsetHeight;
      const chatRatio = chatHeight / containerHeight;
      
      this.addResult(
        'UX-对话中心化',
        chatRatio > 0.6,
        `对话区域占比: ${Math.round(chatRatio * 100)}% (目标: >60%)`
      );
    }

    // 测试快捷操作可达性
    const templateItems = document.querySelectorAll('.ai-template-item');
    this.addResult(
      'UX-快捷操作',
      templateItems.length >= 5,
      `快捷模板数量: ${templateItems.length}`
    );

    // 测试响应式适配
    const isMobile = window.innerWidth <= 768;
    const sidebar = document.getElementById('ai-sidebar-container');
    
    if (sidebar) {
      const sidebarWidth = parseInt(window.getComputedStyle(sidebar).width);
      const expectedWidth = isMobile ? window.innerWidth : 380;
      
      this.addResult(
        'UX-响应式适配',
        Math.abs(sidebarWidth - expectedWidth) < 50,
        `侧边栏宽度: ${sidebarWidth}px (预期: ${expectedWidth}px)`
      );
    }
  }

  /**
   * 运行完整测试套件
   */
  async runCompleteTest() {
    console.log('🚀 开始界面重构集成测试');
    console.log('='.repeat(50));

    try {
      // 等待DOM完全加载
      if (document.readyState !== 'complete') {
        await new Promise(resolve => {
          window.addEventListener('load', resolve);
        });
      }

      // 运行各项测试
      this.testHTMLStructure();
      this.testCSSOptimization();
      await this.testJavaScriptInteractions();
      this.testUserExperience();

      // 生成测试报告
      this.generateTestReport();

    } catch (error) {
      console.error('❌ 测试执行失败:', error);
      this.addResult('测试执行', false, `测试失败: ${error.message}`);
    }
  }

  /**
   * 生成测试报告
   */
  generateTestReport() {
    const endTime = Date.now();
    const duration = endTime - this.startTime;
    
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;
    const successRate = Math.round((passedTests / totalTests) * 100);

    console.log('\n📊 测试报告');
    console.log('='.repeat(50));
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过: ${passedTests}`);
    console.log(`失败: ${failedTests}`);
    console.log(`成功率: ${successRate}%`);
    console.log(`耗时: ${duration}ms`);

    // 详细失败信息
    const failures = this.testResults.filter(r => !r.success);
    if (failures.length > 0) {
      console.log('\n❌ 失败的测试:');
      failures.forEach(failure => {
        console.log(`  - ${failure.testName}: ${failure.message}`);
      });
    }

    // 总体评估
    if (successRate >= 90) {
      console.log('\n🎉 界面重构集成测试: 优秀');
    } else if (successRate >= 80) {
      console.log('\n✅ 界面重构集成测试: 良好');
    } else if (successRate >= 70) {
      console.log('\n⚠️ 界面重构集成测试: 需要改进');
    } else {
      console.log('\n❌ 界面重构集成测试: 需要重大修复');
    }

    return {
      totalTests,
      passedTests,
      failedTests,
      successRate,
      duration,
      status: successRate >= 80 ? 'PASS' : 'FAIL'
    };
  }
}

// 全局测试函数
window.testUIRedesignIntegration = async function() {
  const tester = new UIRedesignIntegrationTester();
  return await tester.runCompleteTest();
};

// 自动运行测试（如果在浏览器环境中）
if (typeof window !== 'undefined' && document.readyState === 'complete') {
  setTimeout(() => {
    window.testUIRedesignIntegration();
  }, 1000);
}

console.log('🧪 界面重构集成测试脚本已加载');
console.log('💡 运行测试: window.testUIRedesignIntegration()');
