/**
 * @file Notion配置验证脚本
 * @description 验证AI侧边栏与Notion集成的配置是否正确
 */

/**
 * 验证Notion配置
 */
async function verifyNotionConfiguration() {
  console.log('🔍 开始验证Notion配置...');
  
  const verificationResults = {
    databaseIds: { tested: false, passed: false, details: {} },
    backgroundService: { tested: false, passed: false, details: {} },
    uiIntegration: { tested: false, passed: false, details: {} },
    apiEndpoints: { tested: false, passed: false, details: {} },
    overall: false
  };
  
  try {
    // 验证1: 数据库ID配置
    console.log('📋 验证数据库ID配置...');
    verificationResults.databaseIds = await verifyDatabaseIds();
    
    // 验证2: Background Service配置
    console.log('🔧 验证Background Service配置...');
    verificationResults.backgroundService = await verifyBackgroundService();
    
    // 验证3: UI集成配置
    console.log('🎨 验证UI集成配置...');
    verificationResults.uiIntegration = await verifyUIIntegration();
    
    // 验证4: API端点配置
    console.log('🌐 验证API端点配置...');
    verificationResults.apiEndpoints = await verifyApiEndpoints();
    
    // 计算总体结果
    verificationResults.overall = Object.values(verificationResults)
      .filter(result => typeof result === 'object' && result.tested)
      .every(result => result.passed);
    
    // 输出验证结果
    printVerificationResults(verificationResults);
    
  } catch (error) {
    console.error('❌ 验证过程中发生错误:', error);
  }
  
  return verificationResults;
}

/**
 * 验证数据库ID配置
 */
async function verifyDatabaseIds() {
  const result = { tested: true, passed: false, details: {} };
  
  try {
    // 预期的数据库ID
    const expectedIds = {
      chatHistory: '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',
      knowledgeBase: '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'
    };
    
    // 检查配置是否正确
    let configCorrect = true;
    const configDetails = {};
    
    // 这里我们无法直接访问后台脚本的配置，但可以通过消息验证
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'ai:notion:connect',
        data: { action: 'get_database_config' }
      });
      
      if (response && response.success) {
        configDetails.chatHistory = response.databases?.chatHistory || '未配置';
        configDetails.knowledgeBase = response.databases?.knowledgeBase || '未配置';
        
        // 验证ID是否匹配
        if (configDetails.chatHistory === expectedIds.chatHistory &&
            configDetails.knowledgeBase === expectedIds.knowledgeBase) {
          configCorrect = true;
        } else {
          configCorrect = false;
        }
      } else {
        configCorrect = false;
        configDetails.error = '无法获取数据库配置';
      }
    } catch (error) {
      configCorrect = false;
      configDetails.error = error.message;
    }
    
    result.passed = configCorrect;
    result.details = {
      expected: expectedIds,
      actual: configDetails,
      match: configCorrect
    };
    
    if (configCorrect) {
      console.log('✅ 数据库ID配置验证通过');
    } else {
      console.error('❌ 数据库ID配置验证失败');
    }
    
  } catch (error) {
    result.details.error = error.message;
    console.error('❌ 数据库ID配置验证异常:', error);
  }
  
  return result;
}

/**
 * 验证Background Service配置
 */
async function verifyBackgroundService() {
  const result = { tested: true, passed: false, details: {} };
  
  try {
    const endpoints = [
      'ai:notion:connect',
      'ai:notion:sync',
      'ai:notion:search',
      'ai:notion:create',
      'ai:notion:disconnect'
    ];
    
    let passedCount = 0;
    const endpointResults = {};
    
    for (const endpoint of endpoints) {
      try {
        const response = await chrome.runtime.sendMessage({
          type: endpoint,
          data: { action: 'test' }
        });
        
        // 只要有响应就算通过（即使是错误响应，说明端点存在）
        if (response !== undefined) {
          passedCount++;
          endpointResults[endpoint] = '✅ 可用';
        } else {
          endpointResults[endpoint] = '❌ 无响应';
        }
      } catch (error) {
        // 如果错误不是"未知消息类型"，说明端点存在但有其他问题
        if (!error.message.includes('未知消息类型')) {
          passedCount++;
          endpointResults[endpoint] = '✅ 可用（有错误但端点存在）';
        } else {
          endpointResults[endpoint] = '❌ 端点不存在';
        }
      }
    }
    
    result.passed = passedCount >= endpoints.length * 0.8;
    result.details = {
      endpoints: endpointResults,
      passedCount: passedCount,
      totalCount: endpoints.length
    };
    
    if (result.passed) {
      console.log('✅ Background Service配置验证通过');
    } else {
      console.error('❌ Background Service配置验证失败');
    }
    
  } catch (error) {
    result.details.error = error.message;
    console.error('❌ Background Service配置验证异常:', error);
  }
  
  return result;
}

/**
 * 验证UI集成配置
 */
async function verifyUIIntegration() {
  const result = { tested: true, passed: false, details: {} };
  
  try {
    const uiElements = {
      notionTab: document.getElementById('ai-main-tab-notion'),
      notionPanel: document.getElementById('ai-panel-notion'),
      connectBtn: document.getElementById('ai-notion-connect-btn'),
      syncBtn: document.getElementById('ai-notion-sync-btn'),
      statusCard: document.getElementById('ai-notion-status-card')
    };
    
    let foundCount = 0;
    const elementResults = {};
    
    for (const [key, element] of Object.entries(uiElements)) {
      if (element) {
        foundCount++;
        elementResults[key] = '✅ 存在';
      } else {
        elementResults[key] = '❌ 缺失';
      }
    }
    
    result.passed = foundCount >= Object.keys(uiElements).length * 0.8;
    result.details = {
      elements: elementResults,
      foundCount: foundCount,
      totalCount: Object.keys(uiElements).length
    };
    
    if (result.passed) {
      console.log('✅ UI集成配置验证通过');
    } else {
      console.error('❌ UI集成配置验证失败');
    }
    
  } catch (error) {
    result.details.error = error.message;
    console.error('❌ UI集成配置验证异常:', error);
  }
  
  return result;
}

/**
 * 验证API端点配置
 */
async function verifyApiEndpoints() {
  const result = { tested: true, passed: false, details: {} };
  
  try {
    // 检查Notion API相关配置
    const apiConfig = {
      baseUrl: 'https://api.notion.com/v1',
      version: '2022-06-28',
      authUrl: 'https://api.notion.com/v1/oauth/authorize',
      tokenUrl: 'https://api.notion.com/v1/oauth/token'
    };
    
    // 验证URL格式
    let configValid = true;
    const configDetails = {};
    
    for (const [key, url] of Object.entries(apiConfig)) {
      try {
        new URL(url);
        configDetails[key] = '✅ 有效URL';
      } catch (urlError) {
        configValid = false;
        configDetails[key] = '❌ 无效URL';
      }
    }
    
    result.passed = configValid;
    result.details = {
      config: apiConfig,
      validation: configDetails,
      valid: configValid
    };
    
    if (configValid) {
      console.log('✅ API端点配置验证通过');
    } else {
      console.error('❌ API端点配置验证失败');
    }
    
  } catch (error) {
    result.details.error = error.message;
    console.error('❌ API端点配置验证异常:', error);
  }
  
  return result;
}

/**
 * 输出验证结果
 */
function printVerificationResults(results) {
  console.log('\n📊 Notion配置验证结果:');
  console.log('==========================================');
  
  console.log(`数据库ID配置: ${results.databaseIds.passed ? '✅ 通过' : '❌ 失败'}`);
  if (results.databaseIds.details.error) {
    console.log(`  错误: ${results.databaseIds.details.error}`);
  } else if (results.databaseIds.details.expected) {
    console.log(`  对话数据库: ${results.databaseIds.details.actual.chatHistory}`);
    console.log(`  知识库数据库: ${results.databaseIds.details.actual.knowledgeBase}`);
  }
  
  console.log(`Background Service: ${results.backgroundService.passed ? '✅ 通过' : '❌ 失败'}`);
  if (results.backgroundService.details.passedCount !== undefined) {
    console.log(`  端点可用性: ${results.backgroundService.details.passedCount}/${results.backgroundService.details.totalCount}`);
  }
  
  console.log(`UI集成: ${results.uiIntegration.passed ? '✅ 通过' : '❌ 失败'}`);
  if (results.uiIntegration.details.foundCount !== undefined) {
    console.log(`  UI元素: ${results.uiIntegration.details.foundCount}/${results.uiIntegration.details.totalCount}`);
  }
  
  console.log(`API端点: ${results.apiEndpoints.passed ? '✅ 通过' : '❌ 失败'}`);
  
  console.log(`\n总体结果: ${results.overall ? '🎉 配置正确' : '⚠️ 需要修复'}`);
  
  if (results.overall) {
    console.log('\n💡 Notion集成配置验证通过:');
    console.log('1. ✅ 数据库ID已正确配置');
    console.log('2. ✅ Background Service端点可用');
    console.log('3. ✅ UI界面集成完整');
    console.log('4. ✅ API端点配置有效');
    console.log('\n🚀 可以开始使用Notion集成功能！');
  } else {
    console.log('\n🔧 需要检查的项目:');
    if (!results.databaseIds.passed) console.log('- 数据库ID配置');
    if (!results.backgroundService.passed) console.log('- Background Service端点');
    if (!results.uiIntegration.passed) console.log('- UI界面集成');
    if (!results.apiEndpoints.passed) console.log('- API端点配置');
  }
}

// 导出函数供控制台调用
window.verifyNotionConfiguration = verifyNotionConfiguration;

// 自动提示
if (typeof chrome !== 'undefined' && chrome.runtime) {
  console.log('🎯 Notion配置验证脚本已加载');
  console.log('💡 运行 verifyNotionConfiguration() 验证配置');
  console.log('📋 预期配置:');
  console.log('  - 对话数据库: 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');
  console.log('  - 知识库数据库: 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');
}
