/**
 * @file AI侧边栏性能集成器
 * @description 集成所有性能优化和功能增强模块的主控制器
 */

import { AiMemoryManager } from '../performance/aiMemoryManager.js';
import { AiApiOptimizer } from '../performance/aiApiOptimizer.js';
import { AiModuleLoader } from '../performance/aiModuleLoader.js';
import { AiUIOptimizer } from '../performance/aiUIOptimizer.js';
import { AiContentSummarizer } from '../content/aiContentSummarizer.js';
import { AiWorkflowEngine } from '../automation/aiWorkflowEngine.js';
import { AiI18nManager } from '../i18n/aiI18nManager.js';
import { AiCollaborationManager } from '../collaboration/aiCollaborationManager.js';

/**
 * @class AiPerformanceIntegrator
 * @description 性能集成器，统一管理所有性能优化和功能增强模块
 */
class AiPerformanceIntegrator {
  /**
   * @function constructor - 构造函数
   * @description 初始化性能集成器
   * @param {Object} coreManagers - 核心管理器实例
   */
  constructor(coreManagers = {}) {
    const {
      apiManager,
      securityManager,
      settingsManager
    } = coreManagers;
    
    // 核心管理器
    this.apiManager = apiManager;
    this.securityManager = securityManager;
    this.settingsManager = settingsManager;
    
    // 性能优化模块
    this.memoryManager = null;
    this.apiOptimizer = null;
    this.moduleLoader = null;
    this.uiOptimizer = null;
    
    // 功能增强模块
    this.contentSummarizer = null;
    this.workflowEngine = null;
    this.i18nManager = null;
    this.collaborationManager = null;
    
    // 集成配置
    this.config = {
      // 初始化配置
      enablePerformanceOptimizations: true,
      enableFeatureEnhancements: true,
      enableLazyLoading: true,
      enableAutoOptimization: true,
      
      // 性能监控配置
      performanceMonitoring: true,
      metricsCollection: true,
      alertThresholds: {
        memoryUsage: 80, // 80%
        apiLatency: 5000, // 5秒
        uiFrameRate: 30 // 30fps
      },
      
      // 自动优化配置
      autoCleanupInterval: 10 * 60 * 1000, // 10分钟
      autoOptimizationInterval: 5 * 60 * 1000, // 5分钟
      performanceCheckInterval: 30 * 1000 // 30秒
    };
    
    // 集成状态
    this.isInitialized = false;
    this.enabledModules = new Set();
    this.performanceMetrics = new Map();
    this.optimizationHistory = [];
    
    // 定时器
    this.performanceTimer = null;
    this.optimizationTimer = null;
    this.cleanupTimer = null;
    
    // 事件监听器
    this.eventListeners = new Map();
    
    // 统计信息
    this.stats = {
      initializationTime: 0,
      totalOptimizations: 0,
      memoryOptimizations: 0,
      apiOptimizations: 0,
      uiOptimizations: 0,
      averagePerformanceScore: 0
    };
  }

  /**
   * @function init - 初始化集成器
   * @description 初始化所有性能优化和功能增强模块
   * @param {Object} options - 初始化选项
   * @returns {Promise<boolean>} 初始化是否成功
   */
  async init(options = {}) {
    const startTime = Date.now();
    
    try {
      console.log('[性能集成] 🚀 开始初始化AI侧边栏性能集成器...');
      
      // 合并配置
      this.config = { ...this.config, ...options };
      
      // 初始化性能优化模块
      if (this.config.enablePerformanceOptimizations) {
        await this.initPerformanceModules();
      }
      
      // 初始化功能增强模块
      if (this.config.enableFeatureEnhancements) {
        await this.initFeatureModules();
      }
      
      // 启动性能监控
      if (this.config.performanceMonitoring) {
        this.startPerformanceMonitoring();
      }
      
      // 启动自动优化
      if (this.config.enableAutoOptimization) {
        this.startAutoOptimization();
      }
      
      // 注册事件监听器
      this.registerEventListeners();
      
      this.isInitialized = true;
      this.stats.initializationTime = Date.now() - startTime;
      
      console.log(`[性能集成] ✅ 性能集成器初始化完成 (${this.stats.initializationTime}ms)`);
      console.log(`[性能集成] 📊 已启用模块: ${Array.from(this.enabledModules).join(', ')}`);
      
      // 触发初始化完成事件
      this.emit('initialized', {
        modules: Array.from(this.enabledModules),
        initTime: this.stats.initializationTime
      });
      
      return true;
      
    } catch (error) {
      console.error('[性能集成] ❌ 初始化失败:', error);
      this.emit('initializationFailed', { error: error.message });
      return false;
    }
  }

  /**
   * @function initPerformanceModules - 初始化性能模块
   * @description 初始化所有性能优化模块
   */
  async initPerformanceModules() {
    console.log('[性能集成] 🔧 初始化性能优化模块...');
    
    // 初始化内存管理器
    try {
      this.memoryManager = new AiMemoryManager();
      await this.memoryManager.init();
      this.enabledModules.add('memoryManager');
      console.log('[性能集成] ✅ 内存管理器已启用');
    } catch (error) {
      console.warn('[性能集成] ⚠️ 内存管理器初始化失败:', error.message);
    }
    
    // 初始化API优化器
    try {
      this.apiOptimizer = new AiApiOptimizer();
      await this.apiOptimizer.init();
      this.enabledModules.add('apiOptimizer');
      console.log('[性能集成] ✅ API优化器已启用');
    } catch (error) {
      console.warn('[性能集成] ⚠️ API优化器初始化失败:', error.message);
    }
    
    // 初始化模块加载器
    try {
      this.moduleLoader = new AiModuleLoader();
      await this.moduleLoader.init();
      this.enabledModules.add('moduleLoader');
      console.log('[性能集成] ✅ 模块加载器已启用');
    } catch (error) {
      console.warn('[性能集成] ⚠️ 模块加载器初始化失败:', error.message);
    }
    
    // 初始化UI优化器
    try {
      this.uiOptimizer = new AiUIOptimizer();
      await this.uiOptimizer.init();
      this.enabledModules.add('uiOptimizer');
      console.log('[性能集成] ✅ UI优化器已启用');
    } catch (error) {
      console.warn('[性能集成] ⚠️ UI优化器初始化失败:', error.message);
    }
  }

  /**
   * @function initFeatureModules - 初始化功能模块
   * @description 初始化所有功能增强模块
   */
  async initFeatureModules() {
    console.log('[性能集成] 🎯 初始化功能增强模块...');
    
    // 初始化内容摘要器
    try {
      if (this.apiManager) {
        this.contentSummarizer = new AiContentSummarizer(this.apiManager);
        this.enabledModules.add('contentSummarizer');
        console.log('[性能集成] ✅ 内容摘要器已启用');
      }
    } catch (error) {
      console.warn('[性能集成] ⚠️ 内容摘要器初始化失败:', error.message);
    }
    
    // 初始化工作流引擎
    try {
      if (this.apiManager) {
        this.workflowEngine = new AiWorkflowEngine(this.apiManager);
        await this.workflowEngine.init();
        this.enabledModules.add('workflowEngine');
        console.log('[性能集成] ✅ 工作流引擎已启用');
      }
    } catch (error) {
      console.warn('[性能集成] ⚠️ 工作流引擎初始化失败:', error.message);
    }
    
    // 初始化国际化管理器
    try {
      this.i18nManager = new AiI18nManager();
      await this.i18nManager.init();
      this.enabledModules.add('i18nManager');
      console.log('[性能集成] ✅ 国际化管理器已启用');
    } catch (error) {
      console.warn('[性能集成] ⚠️ 国际化管理器初始化失败:', error.message);
    }
    
    // 初始化协作管理器
    try {
      if (this.apiManager && this.securityManager) {
        this.collaborationManager = new AiCollaborationManager(this.apiManager, this.securityManager);
        await this.collaborationManager.init();
        this.enabledModules.add('collaborationManager');
        console.log('[性能集成] ✅ 协作管理器已启用');
      }
    } catch (error) {
      console.warn('[性能集成] ⚠️ 协作管理器初始化失败:', error.message);
    }
  }

  /**
   * @function startPerformanceMonitoring - 启动性能监控
   * @description 启动性能监控和指标收集
   */
  startPerformanceMonitoring() {
    if (this.performanceTimer) {
      clearInterval(this.performanceTimer);
    }
    
    this.performanceTimer = setInterval(async () => {
      try {
        await this.collectPerformanceMetrics();
        await this.analyzePerformance();
      } catch (error) {
        console.warn('[性能集成] 性能监控错误:', error);
      }
    }, this.config.performanceCheckInterval);
    
    console.log('[性能集成] 📊 性能监控已启动');
  }

  /**
   * @function collectPerformanceMetrics - 收集性能指标
   * @description 从各个模块收集性能指标
   */
  async collectPerformanceMetrics() {
    const metrics = {
      timestamp: Date.now(),
      memory: null,
      api: null,
      ui: null,
      modules: null
    };
    
    // 收集内存指标
    if (this.memoryManager) {
      try {
        metrics.memory = this.memoryManager.getMemoryStatus();
      } catch (error) {
        console.warn('[性能集成] 内存指标收集失败:', error);
      }
    }
    
    // 收集API指标
    if (this.apiOptimizer) {
      try {
        metrics.api = this.apiOptimizer.getStats();
      } catch (error) {
        console.warn('[性能集成] API指标收集失败:', error);
      }
    }
    
    // 收集UI指标
    if (this.uiOptimizer) {
      try {
        metrics.ui = this.uiOptimizer.getPerformanceMetrics();
      } catch (error) {
        console.warn('[性能集成] UI指标收集失败:', error);
      }
    }
    
    // 收集模块指标
    if (this.moduleLoader) {
      try {
        metrics.modules = this.moduleLoader.getStats();
      } catch (error) {
        console.warn('[性能集成] 模块指标收集失败:', error);
      }
    }
    
    // 存储指标
    this.performanceMetrics.set(metrics.timestamp, metrics);
    
    // 保持指标历史在合理范围内
    if (this.performanceMetrics.size > 100) {
      const oldestKey = this.performanceMetrics.keys().next().value;
      this.performanceMetrics.delete(oldestKey);
    }
  }

  /**
   * @function analyzePerformance - 分析性能
   * @description 分析性能指标并触发优化
   */
  async analyzePerformance() {
    const latestMetrics = Array.from(this.performanceMetrics.values()).pop();
    if (!latestMetrics) return;
    
    const alerts = [];
    
    // 检查内存使用
    if (latestMetrics.memory && latestMetrics.memory.currentUsage) {
      const memoryUsagePercent = (latestMetrics.memory.currentUsage / latestMetrics.memory.thresholds.max) * 100;
      if (memoryUsagePercent > this.config.alertThresholds.memoryUsage) {
        alerts.push({
          type: 'memory',
          severity: 'warning',
          message: `内存使用率过高: ${memoryUsagePercent.toFixed(1)}%`,
          action: 'cleanup'
        });
      }
    }
    
    // 检查API性能
    if (latestMetrics.api && latestMetrics.api.averageResponseTime) {
      if (latestMetrics.api.averageResponseTime > this.config.alertThresholds.apiLatency) {
        alerts.push({
          type: 'api',
          severity: 'warning',
          message: `API响应时间过长: ${latestMetrics.api.averageResponseTime}ms`,
          action: 'optimize'
        });
      }
    }
    
    // 检查UI性能
    if (latestMetrics.ui && latestMetrics.ui.currentFPS) {
      if (latestMetrics.ui.currentFPS < this.config.alertThresholds.uiFrameRate) {
        alerts.push({
          type: 'ui',
          severity: 'warning',
          message: `UI帧率过低: ${latestMetrics.ui.currentFPS}fps`,
          action: 'optimize'
        });
      }
    }
    
    // 处理性能警报
    if (alerts.length > 0) {
      await this.handlePerformanceAlerts(alerts);
    }
    
    // 计算性能评分
    const performanceScore = this.calculatePerformanceScore(latestMetrics);
    this.stats.averagePerformanceScore = performanceScore;
    
    // 触发性能更新事件
    this.emit('performanceUpdated', {
      metrics: latestMetrics,
      alerts: alerts,
      score: performanceScore
    });
  }

  /**
   * @function handlePerformanceAlerts - 处理性能警报
   * @description 根据性能警报执行相应的优化操作
   * @param {Array} alerts - 性能警报数组
   */
  async handlePerformanceAlerts(alerts) {
    for (const alert of alerts) {
      try {
        switch (alert.action) {
          case 'cleanup':
            await this.performMemoryCleanup();
            break;
          case 'optimize':
            await this.performOptimization(alert.type);
            break;
        }
        
        console.log(`[性能集成] 🔧 已处理性能警报: ${alert.message}`);
      } catch (error) {
        console.warn(`[性能集成] 性能警报处理失败: ${alert.message}`, error);
      }
    }
  }

  /**
   * @function performMemoryCleanup - 执行内存清理
   * @description 执行内存清理操作
   */
  async performMemoryCleanup() {
    if (this.memoryManager) {
      await this.memoryManager.performAutoCleanup();
      this.stats.memoryOptimizations++;
      this.stats.totalOptimizations++;
    }
  }

  /**
   * @function performOptimization - 执行优化
   * @description 根据类型执行相应的优化操作
   * @param {string} type - 优化类型
   */
  async performOptimization(type) {
    switch (type) {
      case 'api':
        if (this.apiOptimizer) {
          this.apiOptimizer.clearCache();
          this.stats.apiOptimizations++;
        }
        break;
      case 'ui':
        if (this.uiOptimizer) {
          this.uiOptimizer.cleanupExpiredCache();
          this.stats.uiOptimizations++;
        }
        break;
    }
    
    this.stats.totalOptimizations++;
  }

  /**
   * @function calculatePerformanceScore - 计算性能评分
   * @description 根据性能指标计算综合性能评分
   * @param {Object} metrics - 性能指标
   * @returns {number} 性能评分 (0-100)
   */
  calculatePerformanceScore(metrics) {
    let score = 100;
    let factors = 0;
    
    // 内存评分
    if (metrics.memory && metrics.memory.currentUsage) {
      const memoryScore = Math.max(0, 100 - (metrics.memory.currentUsage / metrics.memory.thresholds.max) * 100);
      score += memoryScore;
      factors++;
    }
    
    // API评分
    if (metrics.api && metrics.api.cacheHitRate) {
      const apiScore = parseFloat(metrics.api.cacheHitRate);
      score += apiScore;
      factors++;
    }
    
    // UI评分
    if (metrics.ui && metrics.ui.currentFPS) {
      const uiScore = Math.min(100, (metrics.ui.currentFPS / 60) * 100);
      score += uiScore;
      factors++;
    }
    
    return factors > 0 ? Math.round(score / (factors + 1)) : 50;
  }

  /**
   * @function startAutoOptimization - 启动自动优化
   * @description 启动自动优化定时任务
   */
  startAutoOptimization() {
    if (this.optimizationTimer) {
      clearInterval(this.optimizationTimer);
    }
    
    this.optimizationTimer = setInterval(async () => {
      try {
        await this.performRoutineOptimization();
      } catch (error) {
        console.warn('[性能集成] 自动优化错误:', error);
      }
    }, this.config.autoOptimizationInterval);
    
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    
    this.cleanupTimer = setInterval(async () => {
      try {
        await this.performRoutineCleanup();
      } catch (error) {
        console.warn('[性能集成] 自动清理错误:', error);
      }
    }, this.config.autoCleanupInterval);
    
    console.log('[性能集成] 🤖 自动优化已启动');
  }

  /**
   * @function performRoutineOptimization - 执行例行优化
   * @description 执行例行的性能优化任务
   */
  async performRoutineOptimization() {
    console.log('[性能集成] 🔄 执行例行优化...');
    
    const optimizations = [];
    
    // API缓存优化
    if (this.apiOptimizer) {
      this.apiOptimizer.cleanupExpiredCache();
      optimizations.push('API缓存清理');
    }
    
    // UI性能优化
    if (this.uiOptimizer) {
      this.uiOptimizer.cleanupExpiredCache();
      optimizations.push('UI缓存清理');
    }
    
    // 模块优化
    if (this.moduleLoader) {
      // 卸载长时间未使用的模块
      const unusedModules = this.moduleLoader.getUnusedModules();
      for (const moduleName of unusedModules) {
        this.moduleLoader.unloadModule(moduleName);
      }
      if (unusedModules.length > 0) {
        optimizations.push(`卸载${unusedModules.length}个未使用模块`);
      }
    }
    
    if (optimizations.length > 0) {
      console.log(`[性能集成] ✅ 例行优化完成: ${optimizations.join(', ')}`);
      this.stats.totalOptimizations++;
    }
  }

  /**
   * @function performRoutineCleanup - 执行例行清理
   * @description 执行例行的清理任务
   */
  async performRoutineCleanup() {
    console.log('[性能集成] 🧹 执行例行清理...');
    
    const cleanupTasks = [];
    
    // 内存清理
    if (this.memoryManager) {
      await this.memoryManager.performAutoCleanup();
      cleanupTasks.push('内存清理');
    }
    
    // 清理性能指标历史
    if (this.performanceMetrics.size > 50) {
      const keysToDelete = Array.from(this.performanceMetrics.keys()).slice(0, -50);
      keysToDelete.forEach(key => this.performanceMetrics.delete(key));
      cleanupTasks.push('性能指标清理');
    }
    
    // 清理优化历史
    if (this.optimizationHistory.length > 100) {
      this.optimizationHistory = this.optimizationHistory.slice(-50);
      cleanupTasks.push('优化历史清理');
    }
    
    if (cleanupTasks.length > 0) {
      console.log(`[性能集成] ✅ 例行清理完成: ${cleanupTasks.join(', ')}`);
    }
  }

  /**
   * @function registerEventListeners - 注册事件监听器
   * @description 注册各种事件监听器
   */
  registerEventListeners() {
    // 监听内存警报
    if (this.memoryManager) {
      this.memoryManager.on('memoryAlert', (data) => {
        this.handleMemoryAlert(data);
      });
    }

    // 监听模块加载事件
    if (this.moduleLoader) {
      this.moduleLoader.on('moduleLoadError', (data) => {
        console.warn(`[性能集成] 模块加载失败: ${data.moduleName}`, data.error);
      });
    }

    // 监听语言变更事件
    if (this.i18nManager) {
      this.i18nManager.addObserver({
        onLanguageChange: (oldLang, newLang) => {
          console.log(`[性能集成] 语言已切换: ${oldLang} -> ${newLang}`);
          this.emit('languageChanged', { oldLang, newLang });
        }
      });
    }

    console.log('[性能集成] 📡 事件监听器已注册');
  }

  /**
   * @function handleMemoryAlert - 处理内存警报
   * @description 处理内存管理器发出的警报
   * @param {Object} alertData - 警报数据
   */
  async handleMemoryAlert(alertData) {
    const { level, usage } = alertData;

    console.warn(`[性能集成] 🚨 内存警报: ${level} - 使用量: ${this.formatBytes(usage)}`);

    if (level === 'critical') {
      // 立即执行紧急清理
      await this.performEmergencyCleanup();
    } else if (level === 'warning') {
      // 执行预防性优化
      await this.performPreventiveOptimization();
    }

    this.emit('memoryAlert', alertData);
  }

  /**
   * @function performEmergencyCleanup - 执行紧急清理
   * @description 在内存使用达到临界值时执行紧急清理
   */
  async performEmergencyCleanup() {
    console.warn('[性能集成] 🚨 执行紧急清理...');

    const cleanupTasks = [];

    // 强制内存清理
    if (this.memoryManager) {
      await this.memoryManager.performEmergencyCleanup();
      cleanupTasks.push('紧急内存清理');
    }

    // 清空所有缓存
    if (this.apiOptimizer) {
      this.apiOptimizer.clearCache();
      cleanupTasks.push('API缓存清空');
    }

    // 卸载非关键模块
    if (this.moduleLoader) {
      const nonCriticalModules = ['cursor', 'workflow', 'collaboration'];
      for (const moduleName of nonCriticalModules) {
        this.moduleLoader.unloadModule(moduleName);
      }
      cleanupTasks.push('非关键模块卸载');
    }

    console.warn(`[性能集成] 🚨 紧急清理完成: ${cleanupTasks.join(', ')}`);
    this.stats.totalOptimizations++;
  }

  /**
   * @function performPreventiveOptimization - 执行预防性优化
   * @description 执行预防性性能优化
   */
  async performPreventiveOptimization() {
    console.log('[性能集成] 🛡️ 执行预防性优化...');

    const optimizations = [];

    // 预防性内存清理
    if (this.memoryManager) {
      await this.memoryManager.performPreventiveCleanup();
      optimizations.push('预防性内存清理');
    }

    // 优化API缓存
    if (this.apiOptimizer) {
      this.apiOptimizer.cleanupExpiredCache();
      optimizations.push('API缓存优化');
    }

    // UI性能优化
    if (this.uiOptimizer) {
      this.uiOptimizer.cleanupExpiredCache();
      optimizations.push('UI性能优化');
    }

    console.log(`[性能集成] 🛡️ 预防性优化完成: ${optimizations.join(', ')}`);
    this.stats.totalOptimizations++;
  }

  /**
   * @function getModuleInstance - 获取模块实例
   * @description 获取指定模块的实例
   * @param {string} moduleName - 模块名称
   * @returns {Object|null} 模块实例或null
   */
  getModuleInstance(moduleName) {
    const moduleMap = {
      'memoryManager': this.memoryManager,
      'apiOptimizer': this.apiOptimizer,
      'moduleLoader': this.moduleLoader,
      'uiOptimizer': this.uiOptimizer,
      'contentSummarizer': this.contentSummarizer,
      'workflowEngine': this.workflowEngine,
      'i18nManager': this.i18nManager,
      'collaborationManager': this.collaborationManager
    };

    return moduleMap[moduleName] || null;
  }

  /**
   * @function isModuleEnabled - 检查模块是否启用
   * @description 检查指定模块是否已启用
   * @param {string} moduleName - 模块名称
   * @returns {boolean} 是否启用
   */
  isModuleEnabled(moduleName) {
    return this.enabledModules.has(moduleName);
  }

  /**
   * @function getEnabledModules - 获取已启用模块
   * @description 获取所有已启用的模块列表
   * @returns {Array} 已启用模块列表
   */
  getEnabledModules() {
    return Array.from(this.enabledModules);
  }

  /**
   * @function getPerformanceReport - 获取性能报告
   * @description 生成详细的性能报告
   * @returns {Object} 性能报告
   */
  getPerformanceReport() {
    const latestMetrics = Array.from(this.performanceMetrics.values()).pop();

    return {
      overview: {
        isInitialized: this.isInitialized,
        enabledModules: Array.from(this.enabledModules),
        performanceScore: this.stats.averagePerformanceScore,
        totalOptimizations: this.stats.totalOptimizations
      },
      metrics: latestMetrics || {},
      stats: this.stats,
      moduleStats: {
        memory: this.memoryManager?.getMemoryStatus() || null,
        api: this.apiOptimizer?.getStats() || null,
        ui: this.uiOptimizer?.getPerformanceMetrics() || null,
        modules: this.moduleLoader?.getStats() || null,
        content: this.contentSummarizer?.getStats() || null,
        workflow: this.workflowEngine?.getStats() || null,
        i18n: this.i18nManager?.getStats() || null,
        collaboration: this.collaborationManager?.getStats() || null
      },
      recommendations: this.generateRecommendations()
    };
  }

  /**
   * @function generateRecommendations - 生成优化建议
   * @description 基于当前性能状态生成优化建议
   * @returns {Array} 优化建议列表
   */
  generateRecommendations() {
    const recommendations = [];
    const latestMetrics = Array.from(this.performanceMetrics.values()).pop();

    if (!latestMetrics) {
      return recommendations;
    }

    // 内存优化建议
    if (latestMetrics.memory && latestMetrics.memory.currentUsage) {
      const memoryUsagePercent = (latestMetrics.memory.currentUsage / latestMetrics.memory.thresholds.max) * 100;
      if (memoryUsagePercent > 70) {
        recommendations.push({
          type: 'memory',
          priority: 'high',
          title: '内存使用率过高',
          description: `当前内存使用率为 ${memoryUsagePercent.toFixed(1)}%，建议执行内存清理`,
          action: 'performMemoryCleanup'
        });
      }
    }

    // API优化建议
    if (latestMetrics.api && latestMetrics.api.cacheHitRate) {
      const hitRate = parseFloat(latestMetrics.api.cacheHitRate);
      if (hitRate < 50) {
        recommendations.push({
          type: 'api',
          priority: 'medium',
          title: 'API缓存命中率较低',
          description: `当前缓存命中率为 ${hitRate}%，建议优化缓存策略`,
          action: 'optimizeApiCache'
        });
      }
    }

    // UI性能建议
    if (latestMetrics.ui && latestMetrics.ui.currentFPS) {
      if (latestMetrics.ui.currentFPS < 30) {
        recommendations.push({
          type: 'ui',
          priority: 'high',
          title: 'UI帧率过低',
          description: `当前帧率为 ${latestMetrics.ui.currentFPS}fps，建议优化UI渲染`,
          action: 'optimizeUIPerformance'
        });
      }
    }

    return recommendations;
  }

  /**
   * @function formatBytes - 格式化字节数
   * @description 将字节数格式化为可读的字符串
   * @param {number} bytes - 字节数
   * @returns {string} 格式化的字符串
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * @function on - 添加事件监听器
   * @description 添加事件监听器
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(callback);
  }

  /**
   * @function emit - 触发事件
   * @description 触发事件并调用所有监听器
   * @param {string} event - 事件名称
   * @param {*} data - 事件数据
   */
  emit(event, data) {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`[性能集成] 事件监听器错误 (${event}):`, error);
        }
      });
    }
  }

  /**
   * @function cleanup - 清理资源
   * @description 清理性能集成器使用的资源
   */
  cleanup() {
    // 清理定时器
    if (this.performanceTimer) {
      clearInterval(this.performanceTimer);
      this.performanceTimer = null;
    }

    if (this.optimizationTimer) {
      clearInterval(this.optimizationTimer);
      this.optimizationTimer = null;
    }

    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }

    // 清理各个模块
    const modules = [
      this.memoryManager,
      this.apiOptimizer,
      this.moduleLoader,
      this.uiOptimizer,
      this.contentSummarizer,
      this.workflowEngine,
      this.i18nManager,
      this.collaborationManager
    ];

    modules.forEach(module => {
      if (module && typeof module.cleanup === 'function') {
        try {
          module.cleanup();
        } catch (error) {
          console.warn('[性能集成] 模块清理失败:', error);
        }
      }
    });

    // 清理状态
    this.enabledModules.clear();
    this.performanceMetrics.clear();
    this.optimizationHistory = [];
    this.eventListeners.clear();

    this.isInitialized = false;

    console.log('[性能集成] 性能集成器已清理');
  }
}

// 导出性能集成器类
export { AiPerformanceIntegrator };
