/**
 * @file AI侧边栏国际化管理器
 * @description 提供多语言支持、自动语言检测和本地化功能
 */

/**
 * @class AiI18nManager
 * @description 国际化管理器，支持多语言界面和内容本地化
 */
class AiI18nManager {
  /**
   * @function constructor - 构造函数
   * @description 初始化国际化管理器
   */
  constructor() {
    // 支持的语言配置
    this.supportedLanguages = new Map([
      ['zh-CN', {
        name: '简体中文',
        nativeName: '简体中文',
        direction: 'ltr',
        dateFormat: 'YYYY年MM月DD日',
        numberFormat: 'zh-CN',
        currency: 'CNY',
        flag: '🇨🇳'
      }],
      ['zh-TW', {
        name: '繁體中文',
        nativeName: '繁體中文',
        direction: 'ltr',
        dateFormat: 'YYYY年MM月DD日',
        numberFormat: 'zh-TW',
        currency: 'TWD',
        flag: '🇹🇼'
      }],
      ['en-US', {
        name: 'English',
        nativeName: 'English',
        direction: 'ltr',
        dateFormat: 'MM/DD/YYYY',
        numberFormat: 'en-US',
        currency: 'USD',
        flag: '🇺🇸'
      }],
      ['ja-JP', {
        name: '日本語',
        nativeName: '日本語',
        direction: 'ltr',
        dateFormat: 'YYYY年MM月DD日',
        numberFormat: 'ja-JP',
        currency: 'JPY',
        flag: '🇯🇵'
      }],
      ['es-ES', {
        name: 'Español',
        nativeName: 'Español',
        direction: 'ltr',
        dateFormat: 'DD/MM/YYYY',
        numberFormat: 'es-ES',
        currency: 'EUR',
        flag: '🇪🇸'
      }],
      ['ar-SA', {
        name: 'العربية',
        nativeName: 'العربية',
        direction: 'rtl',
        dateFormat: 'DD/MM/YYYY',
        numberFormat: 'ar-SA',
        currency: 'SAR',
        flag: '🇸🇦'
      }],
      ['he-IL', {
        name: 'עברית',
        nativeName: 'עברית',
        direction: 'rtl',
        dateFormat: 'DD/MM/YYYY',
        numberFormat: 'he-IL',
        currency: 'ILS',
        flag: '🇮🇱'
      }]
    ]);

    // 当前语言设置
    this.currentLanguage = 'zh-CN';
    this.fallbackLanguage = 'en-US';

    // 翻译数据存储
    this.translations = new Map();
    this.loadedLanguages = new Set();

    // 语言检测配置
    this.detectionConfig = {
      enabled: true,
      confidence: 0.7,
      sampleSize: 200,
      cacheResults: true
    };

    // 缓存
    this.detectionCache = new Map();
    this.translationCache = new Map();

    // 观察器
    this.observers = new Set();
    this.mutationObserver = null;

    // 统计信息
    this.stats = {
      detections: 0,
      translations: 0,
      cacheHits: 0,
      languageSwitches: 0
    };

    // 初始化
    this.init();
  }

  /**
   * @function init - 初始化国际化管理器
   * @description 初始化管理器并加载默认语言
   */
  async init() {
    console.log('[国际化] 初始化国际化管理器...');

    // 检测用户首选语言
    await this.detectUserLanguage();

    // 加载默认翻译
    await this.loadDefaultTranslations();

    // 设置DOM观察器
    this.setupDOMObserver();

    // 应用初始本地化
    this.applyLocalization();

    console.log(`[国际化] 国际化管理器初始化完成，当前语言: ${this.currentLanguage}`);
  }

  /**
   * @function detectUserLanguage - 检测用户语言
   * @description 自动检测用户的首选语言
   */
  async detectUserLanguage() {
    try {
      // 1. 从存储中获取用户设置
      const stored = await chrome.storage.sync.get(['ai_language']);
      if (stored.ai_language && this.supportedLanguages.has(stored.ai_language)) {
        this.currentLanguage = stored.ai_language;
        return;
      }

      // 2. 从浏览器语言设置检测
      const browserLanguages = navigator.languages || [navigator.language];
      for (const lang of browserLanguages) {
        // 精确匹配
        if (this.supportedLanguages.has(lang)) {
          this.currentLanguage = lang;
          return;
        }

        // 语言代码匹配（如 'zh' 匹配 'zh-CN'）
        const langCode = lang.split('-')[0];
        for (const [supportedLang] of this.supportedLanguages) {
          if (supportedLang.startsWith(langCode)) {
            this.currentLanguage = supportedLang;
            return;
          }
        }
      }

      // 3. 从页面内容检测
      const detectedLang = await this.detectContentLanguage(document.body.textContent);
      if (detectedLang && this.supportedLanguages.has(detectedLang)) {
        this.currentLanguage = detectedLang;
        return;
      }

    } catch (error) {
      console.warn('[国际化] 语言检测失败，使用默认语言:', error);
    }
  }

  /**
   * @function detectContentLanguage - 检测内容语言
   * @description 通过内容分析检测语言
   * @param {string} text - 要检测的文本
   * @returns {Promise<string|null>} 检测到的语言代码
   */
  async detectContentLanguage(text) {
    if (!this.detectionConfig.enabled || !text) {
      return null;
    }

    // 检查缓存
    const cacheKey = this.hashText(text.substring(0, this.detectionConfig.sampleSize));
    if (this.detectionCache.has(cacheKey)) {
      this.stats.cacheHits++;
      return this.detectionCache.get(cacheKey);
    }

    this.stats.detections++;

    try {
      const sample = text.substring(0, this.detectionConfig.sampleSize);
      const detectedLang = this.analyzeTextLanguage(sample);

      // 缓存结果
      if (this.detectionConfig.cacheResults) {
        this.detectionCache.set(cacheKey, detectedLang);
      }

      return detectedLang;
    } catch (error) {
      console.warn('[国际化] 内容语言检测失败:', error);
      return null;
    }
  }

  /**
   * @function analyzeTextLanguage - 分析文本语言
   * @description 通过字符特征分析文本语言
   * @param {string} text - 文本样本
   * @returns {string|null} 语言代码
   */
  analyzeTextLanguage(text) {
    const languagePatterns = [
      {
        lang: 'zh-CN',
        patterns: [
          /[\u4e00-\u9fff]/g, // 中文字符
          /[的了在是有和人这一个]/g // 常见中文词
        ],
        weight: 1.0
      },
      {
        lang: 'ja-JP',
        patterns: [
          /[\u3040-\u309f]/g, // 平假名
          /[\u30a0-\u30ff]/g, // 片假名
          /[はのをがにでと]/g // 常见日文助词
        ],
        weight: 1.0
      },
      {
        lang: 'ar-SA',
        patterns: [
          /[\u0600-\u06ff]/g, // 阿拉伯文字符
          /[والتيفيمنهذا]/g // 常见阿拉伯文词
        ],
        weight: 1.0
      },
      {
        lang: 'he-IL',
        patterns: [
          /[\u0590-\u05ff]/g, // 希伯来文字符
          /[שלאתהזהםכל]/g // 常见希伯来文词
        ],
        weight: 1.0
      },
      {
        lang: 'es-ES',
        patterns: [
          /[ñáéíóúü]/gi, // 西班牙文特殊字符
          /\b(el|la|de|que|y|en|un|es|se|no|te|lo|le|da|su|por|son|con|para|al|del|los|las|una)\b/gi
        ],
        weight: 0.8
      },
      {
        lang: 'en-US',
        patterns: [
          /\b(the|be|to|of|and|a|in|that|have|i|it|for|not|on|with|he|as|you|do|at)\b/gi
        ],
        weight: 0.6
      }
    ];

    let bestMatch = null;
    let bestScore = 0;

    for (const { lang, patterns, weight } of languagePatterns) {
      let score = 0;

      for (const pattern of patterns) {
        const matches = text.match(pattern);
        if (matches) {
          score += matches.length * weight;
        }
      }

      // 标准化分数
      const normalizedScore = score / text.length;

      if (normalizedScore > bestScore && normalizedScore > this.detectionConfig.confidence) {
        bestScore = normalizedScore;
        bestMatch = lang;
      }
    }

    return bestMatch;
  }

  /**
   * @function loadDefaultTranslations - 加载默认翻译
   * @description 加载内置的翻译数据
   */
  async loadDefaultTranslations() {
    // 内置翻译数据
    const defaultTranslations = {
      'zh-CN': {
        'app.name': 'AI侧边栏',
        'app.description': '智能AI助手侧边栏',
        'tab.chat': '聊天',
        'tab.analysis': '分析',
        'tab.notion': 'Notion',
        'tab.enhance': '增强',
        'tab.settings': '设置',
        'button.send': '发送',
        'button.clear': '清空',
        'button.save': '保存',
        'button.cancel': '取消',
        'button.confirm': '确认',
        'placeholder.input': '请输入消息...',
        'message.loading': '加载中...',
        'message.error': '发生错误',
        'message.success': '操作成功',
        'setting.language': '语言设置',
        'setting.theme': '主题设置',
        'setting.notification': '通知设置'
      },
      'en-US': {
        'app.name': 'AI Sidebar',
        'app.description': 'Intelligent AI Assistant Sidebar',
        'tab.chat': 'Chat',
        'tab.analysis': 'Analysis',
        'tab.notion': 'Notion',
        'tab.enhance': 'Enhance',
        'tab.settings': 'Settings',
        'button.send': 'Send',
        'button.clear': 'Clear',
        'button.save': 'Save',
        'button.cancel': 'Cancel',
        'button.confirm': 'Confirm',
        'placeholder.input': 'Enter message...',
        'message.loading': 'Loading...',
        'message.error': 'An error occurred',
        'message.success': 'Operation successful',
        'setting.language': 'Language',
        'setting.theme': 'Theme',
        'setting.notification': 'Notifications'
      },
      'ja-JP': {
        'app.name': 'AIサイドバー',
        'app.description': 'インテリジェントAIアシスタントサイドバー',
        'tab.chat': 'チャット',
        'tab.analysis': '分析',
        'tab.notion': 'Notion',
        'tab.enhance': '拡張',
        'tab.settings': '設定',
        'button.send': '送信',
        'button.clear': 'クリア',
        'button.save': '保存',
        'button.cancel': 'キャンセル',
        'button.confirm': '確認',
        'placeholder.input': 'メッセージを入力...',
        'message.loading': '読み込み中...',
        'message.error': 'エラーが発生しました',
        'message.success': '操作が成功しました',
        'setting.language': '言語設定',
        'setting.theme': 'テーマ設定',
        'setting.notification': '通知設定'
      },
      'es-ES': {
        'app.name': 'Barra Lateral AI',
        'app.description': 'Barra Lateral de Asistente AI Inteligente',
        'tab.chat': 'Chat',
        'tab.analysis': 'Análisis',
        'tab.notion': 'Notion',
        'tab.enhance': 'Mejorar',
        'tab.settings': 'Configuración',
        'button.send': 'Enviar',
        'button.clear': 'Limpiar',
        'button.save': 'Guardar',
        'button.cancel': 'Cancelar',
        'button.confirm': 'Confirmar',
        'placeholder.input': 'Ingrese mensaje...',
        'message.loading': 'Cargando...',
        'message.error': 'Ocurrió un error',
        'message.success': 'Operación exitosa',
        'setting.language': 'Idioma',
        'setting.theme': 'Tema',
        'setting.notification': 'Notificaciones'
      }
    };

    // 加载翻译数据
    for (const [lang, translations] of Object.entries(defaultTranslations)) {
      this.translations.set(lang, translations);
      this.loadedLanguages.add(lang);
    }

    console.log(`[国际化] 已加载 ${this.loadedLanguages.size} 种语言的翻译`);
  }

  /**
   * @function setLanguage - 设置语言
   * @description 切换到指定语言
   * @param {string} languageCode - 语言代码
   * @returns {Promise<boolean>} 是否成功切换
   */
  async setLanguage(languageCode) {
    if (!this.supportedLanguages.has(languageCode)) {
      console.warn(`[国际化] 不支持的语言: ${languageCode}`);
      return false;
    }

    if (this.currentLanguage === languageCode) {
      return true;
    }

    try {
      console.log(`[国际化] 切换语言: ${this.currentLanguage} -> ${languageCode}`);

      const oldLanguage = this.currentLanguage;
      this.currentLanguage = languageCode;

      // 确保翻译已加载
      if (!this.loadedLanguages.has(languageCode)) {
        await this.loadLanguageTranslations(languageCode);
      }

      // 应用本地化
      this.applyLocalization();

      // 保存设置
      await chrome.storage.sync.set({ ai_language: languageCode });

      // 通知观察器
      this.notifyLanguageChange(oldLanguage, languageCode);

      this.stats.languageSwitches++;

      console.log(`[国际化] ✅ 语言切换完成: ${languageCode}`);
      return true;

    } catch (error) {
      console.error('[国际化] 语言切换失败:', error);
      this.currentLanguage = this.currentLanguage; // 回滚
      return false;
    }
  }

  /**
   * @function translate - 翻译文本
   * @description 翻译指定的键或文本
   * @param {string} key - 翻译键
   * @param {Object} params - 参数对象
   * @param {string} fallbackLang - 回退语言
   * @returns {string} 翻译后的文本
   */
  translate(key, params = {}, fallbackLang = null) {
    this.stats.translations++;

    // 检查缓存
    const cacheKey = `${this.currentLanguage}:${key}:${JSON.stringify(params)}`;
    if (this.translationCache.has(cacheKey)) {
      this.stats.cacheHits++;
      return this.translationCache.get(cacheKey);
    }

    let translation = this.getTranslation(key, this.currentLanguage);

    // 回退到指定语言
    if (!translation && fallbackLang) {
      translation = this.getTranslation(key, fallbackLang);
    }

    // 回退到默认语言
    if (!translation) {
      translation = this.getTranslation(key, this.fallbackLanguage);
    }

    // 最后回退到键本身
    if (!translation) {
      translation = key;
    }

    // 参数替换
    if (params && Object.keys(params).length > 0) {
      translation = this.interpolateParams(translation, params);
    }

    // 缓存结果
    this.translationCache.set(cacheKey, translation);

    return translation;
  }

  /**
   * @function getTranslation - 获取翻译
   * @description 从翻译数据中获取指定键的翻译
   * @param {string} key - 翻译键
   * @param {string} language - 语言代码
   * @returns {string|null} 翻译文本或null
   */
  getTranslation(key, language) {
    const translations = this.translations.get(language);
    if (!translations) {
      return null;
    }

    // 支持嵌套键（如 'app.name'）
    const keys = key.split('.');
    let value = translations;

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        return null;
      }
    }

    return typeof value === 'string' ? value : null;
  }

  /**
   * @function interpolateParams - 插值参数
   * @description 在翻译文本中插值参数
   * @param {string} text - 翻译文本
   * @param {Object} params - 参数对象
   * @returns {string} 插值后的文本
   */
  interpolateParams(text, params) {
    return text.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return params[key] !== undefined ? params[key] : match;
    });
  }

  /**
   * @function applyLocalization - 应用本地化
   * @description 应用当前语言的本地化设置
   */
  applyLocalization() {
    const langConfig = this.supportedLanguages.get(this.currentLanguage);
    if (!langConfig) return;

    // 设置文档方向
    document.documentElement.dir = langConfig.direction;
    document.documentElement.lang = this.currentLanguage;

    // 应用RTL样式
    if (langConfig.direction === 'rtl') {
      document.body.classList.add('ai-rtl');
    } else {
      document.body.classList.remove('ai-rtl');
    }

    // 更新所有带有翻译属性的元素
    this.updateTranslatableElements();

    // 触发本地化事件
    this.notifyLocalizationChange();
  }

  /**
   * @function updateTranslatableElements - 更新可翻译元素
   * @description 更新页面中所有可翻译的元素
   */
  updateTranslatableElements() {
    // 查找所有带有data-i18n属性的元素
    const translatableElements = document.querySelectorAll('[data-i18n]');

    translatableElements.forEach(element => {
      const key = element.getAttribute('data-i18n');
      const params = this.parseElementParams(element);
      const translation = this.translate(key, params);

      // 根据元素类型设置翻译
      if (element.tagName === 'INPUT' && (element.type === 'text' || element.type === 'search')) {
        element.placeholder = translation;
      } else if (element.hasAttribute('title')) {
        element.title = translation;
      } else {
        element.textContent = translation;
      }
    });

    // 更新带有data-i18n-html属性的元素（支持HTML内容）
    const htmlElements = document.querySelectorAll('[data-i18n-html]');
    htmlElements.forEach(element => {
      const key = element.getAttribute('data-i18n-html');
      const params = this.parseElementParams(element);
      const translation = this.translate(key, params);
      element.innerHTML = translation;
    });
  }

  /**
   * @function parseElementParams - 解析元素参数
   * @description 从元素的data属性中解析翻译参数
   * @param {HTMLElement} element - DOM元素
   * @returns {Object} 参数对象
   */
  parseElementParams(element) {
    const params = {};

    // 解析data-i18n-params属性
    const paramsAttr = element.getAttribute('data-i18n-params');
    if (paramsAttr) {
      try {
        Object.assign(params, JSON.parse(paramsAttr));
      } catch (error) {
        console.warn('[国际化] 参数解析失败:', paramsAttr, error);
      }
    }

    // 解析其他data-i18n-*属性
    for (const attr of element.attributes) {
      if (attr.name.startsWith('data-i18n-') && attr.name !== 'data-i18n-params') {
        const paramName = attr.name.replace('data-i18n-', '');
        params[paramName] = attr.value;
      }
    }

    return params;
  }

  /**
   * @function setupDOMObserver - 设置DOM观察器
   * @description 设置MutationObserver来监听DOM变化
   */
  setupDOMObserver() {
    if (this.mutationObserver) {
      this.mutationObserver.disconnect();
    }

    this.mutationObserver = new MutationObserver((mutations) => {
      let hasNewTranslatableElements = false;

      mutations.forEach(mutation => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // 检查新添加的元素是否包含可翻译内容
              if (node.hasAttribute('data-i18n') ||
                  node.hasAttribute('data-i18n-html') ||
                  node.querySelector('[data-i18n], [data-i18n-html]')) {
                hasNewTranslatableElements = true;
              }
            }
          });
        }
      });

      if (hasNewTranslatableElements) {
        // 延迟更新，避免频繁操作
        setTimeout(() => this.updateTranslatableElements(), 100);
      }
    });

    this.mutationObserver.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  /**
   * @function loadLanguageTranslations - 加载语言翻译
   * @description 动态加载指定语言的翻译数据
   * @param {string} languageCode - 语言代码
   * @returns {Promise<boolean>} 是否加载成功
   */
  async loadLanguageTranslations(languageCode) {
    if (this.loadedLanguages.has(languageCode)) {
      return true;
    }

    try {
      // 尝试从远程加载翻译文件
      const response = await fetch(`/locales/${languageCode}.json`);
      if (response.ok) {
        const translations = await response.json();
        this.translations.set(languageCode, translations);
        this.loadedLanguages.add(languageCode);
        console.log(`[国际化] 已加载语言包: ${languageCode}`);
        return true;
      }
    } catch (error) {
      console.warn(`[国际化] 语言包加载失败: ${languageCode}`, error);
    }

    return false;
  }

  /**
   * @function formatDate - 格式化日期
   * @description 根据当前语言格式化日期
   * @param {Date} date - 日期对象
   * @param {Object} options - 格式化选项
   * @returns {string} 格式化后的日期字符串
   */
  formatDate(date, options = {}) {
    const langConfig = this.supportedLanguages.get(this.currentLanguage);
    const locale = langConfig ? this.currentLanguage : this.fallbackLanguage;

    try {
      return new Intl.DateTimeFormat(locale, options).format(date);
    } catch (error) {
      console.warn('[国际化] 日期格式化失败:', error);
      return date.toLocaleDateString();
    }
  }

  /**
   * @function formatNumber - 格式化数字
   * @description 根据当前语言格式化数字
   * @param {number} number - 数字
   * @param {Object} options - 格式化选项
   * @returns {string} 格式化后的数字字符串
   */
  formatNumber(number, options = {}) {
    const langConfig = this.supportedLanguages.get(this.currentLanguage);
    const locale = langConfig ? this.currentLanguage : this.fallbackLanguage;

    try {
      return new Intl.NumberFormat(locale, options).format(number);
    } catch (error) {
      console.warn('[国际化] 数字格式化失败:', error);
      return number.toString();
    }
  }

  /**
   * @function formatCurrency - 格式化货币
   * @description 根据当前语言格式化货币
   * @param {number} amount - 金额
   * @param {string} currency - 货币代码（可选）
   * @returns {string} 格式化后的货币字符串
   */
  formatCurrency(amount, currency = null) {
    const langConfig = this.supportedLanguages.get(this.currentLanguage);
    const locale = langConfig ? this.currentLanguage : this.fallbackLanguage;
    const currencyCode = currency || (langConfig ? langConfig.currency : 'USD');

    try {
      return new Intl.NumberFormat(locale, {
        style: 'currency',
        currency: currencyCode
      }).format(amount);
    } catch (error) {
      console.warn('[国际化] 货币格式化失败:', error);
      return `${currencyCode} ${amount}`;
    }
  }

  /**
   * @function hashText - 文本哈希
   * @description 为文本生成简单哈希
   * @param {string} text - 输入文本
   * @returns {string} 哈希值
   */
  hashText(text) {
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * @function notifyLanguageChange - 通知语言变更
   * @description 通知观察器语言已变更
   * @param {string} oldLanguage - 旧语言
   * @param {string} newLanguage - 新语言
   */
  notifyLanguageChange(oldLanguage, newLanguage) {
    for (const observer of this.observers) {
      try {
        observer.onLanguageChange(oldLanguage, newLanguage);
      } catch (error) {
        console.error('[国际化] 观察器通知失败:', error);
      }
    }

    // 触发自定义事件
    const event = new CustomEvent('ai:language:changed', {
      detail: { oldLanguage, newLanguage }
    });
    document.dispatchEvent(event);
  }

  /**
   * @function notifyLocalizationChange - 通知本地化变更
   * @description 通知观察器本地化已变更
   */
  notifyLocalizationChange() {
    for (const observer of this.observers) {
      try {
        if (observer.onLocalizationChange) {
          observer.onLocalizationChange(this.currentLanguage);
        }
      } catch (error) {
        console.error('[国际化] 本地化通知失败:', error);
      }
    }

    // 触发自定义事件
    const event = new CustomEvent('ai:localization:changed', {
      detail: { language: this.currentLanguage }
    });
    document.dispatchEvent(event);
  }

  /**
   * @function addObserver - 添加观察器
   * @description 添加语言变更观察器
   * @param {Object} observer - 观察器对象
   */
  addObserver(observer) {
    this.observers.add(observer);
  }

  /**
   * @function removeObserver - 移除观察器
   * @description 移除语言变更观察器
   * @param {Object} observer - 观察器对象
   */
  removeObserver(observer) {
    this.observers.delete(observer);
  }

  /**
   * @function getSupportedLanguages - 获取支持的语言
   * @description 获取所有支持的语言列表
   * @returns {Array} 语言列表
   */
  getSupportedLanguages() {
    return Array.from(this.supportedLanguages.entries()).map(([code, config]) => ({
      code,
      name: config.name,
      nativeName: config.nativeName,
      direction: config.direction,
      flag: config.flag
    }));
  }

  /**
   * @function getCurrentLanguage - 获取当前语言
   * @description 获取当前设置的语言
   * @returns {Object} 当前语言信息
   */
  getCurrentLanguage() {
    const config = this.supportedLanguages.get(this.currentLanguage);
    return {
      code: this.currentLanguage,
      ...config
    };
  }

  /**
   * @function getStats - 获取统计信息
   * @description 获取国际化管理器的统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      ...this.stats,
      currentLanguage: this.currentLanguage,
      supportedLanguages: this.supportedLanguages.size,
      loadedLanguages: this.loadedLanguages.size,
      cacheSize: this.detectionCache.size + this.translationCache.size,
      cacheHitRate: this.stats.detections + this.stats.translations > 0 ?
        (this.stats.cacheHits / (this.stats.detections + this.stats.translations) * 100).toFixed(2) + '%' : '0%'
    };
  }

  /**
   * @function cleanup - 清理资源
   * @description 清理国际化管理器使用的资源
   */
  cleanup() {
    if (this.mutationObserver) {
      this.mutationObserver.disconnect();
      this.mutationObserver = null;
    }

    this.observers.clear();
    this.detectionCache.clear();
    this.translationCache.clear();

    console.log('[国际化] 国际化管理器已清理');
  }
}

// 导出国际化管理器类
export { AiI18nManager };