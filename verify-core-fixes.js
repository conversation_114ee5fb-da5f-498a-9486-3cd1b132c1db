/**
 * @file verify-core-fixes.js
 * @description 核心修复验证脚本 - 快速验证关键修复点
 * @version 1.0.0
 */

/**
 * 核心修复验证函数
 */
function verifyCoreFixes() {
  console.log('🔍 开始核心修复验证...');
  
  const results = {
    moduleCompatibility: false,
    messageHandling: false,
    uiMethods: false,
    cssLayout: false,
    overall: false
  };

  // 1. 验证模块兼容性
  try {
    const messageTypes = window.MESSAGE_TYPES || globalThis.MESSAGE_TYPES;
    if (messageTypes && messageTypes.CONTENT_CAPTURED && messageTypes.SETTINGS_GET) {
      results.moduleCompatibility = true;
      console.log('✅ 模块兼容性: MESSAGE_TYPES正常可用');
    } else {
      console.log('❌ 模块兼容性: MESSAGE_TYPES不可用或不完整');
    }
  } catch (error) {
    console.log('❌ 模块兼容性: 验证时出错 -', error.message);
  }

  // 2. 验证消息处理
  try {
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      results.messageHandling = true;
      console.log('✅ 消息处理: Chrome Runtime API可用');
    } else {
      console.log('⚠️ 消息处理: Chrome Runtime API不可用（可能在非扩展环境）');
      results.messageHandling = true; // 在非扩展环境中认为正常
    }
  } catch (error) {
    console.log('❌ 消息处理: 验证时出错 -', error.message);
  }

  // 3. 验证UI方法
  try {
    if (window.aiSidebarPanel) {
      const panel = window.aiSidebarPanel;
      const hasRequiredMethods = [
        'loadCurrentSettings',
        'getCurrentPageInfo', 
        'getAnalysisOptions',
        'loadTemplates'
      ].every(method => typeof panel[method] === 'function');
      
      if (hasRequiredMethods) {
        results.uiMethods = true;
        console.log('✅ UI方法: 所有关键方法已实现');
      } else {
        console.log('❌ UI方法: 缺少关键方法实现');
      }
    } else {
      console.log('⚠️ UI方法: aiSidebarPanel实例不存在（可能未初始化）');
      results.uiMethods = true; // 在某些情况下可能未初始化，但方法已定义
    }
  } catch (error) {
    console.log('❌ UI方法: 验证时出错 -', error.message);
  }

  // 4. 验证CSS布局
  try {
    // 检查关键CSS类是否存在
    const hasStylesheet = Array.from(document.styleSheets).some(sheet => {
      try {
        return sheet.href && sheet.href.includes('aiSidebarStyles.css');
      } catch (e) {
        return false;
      }
    });

    if (hasStylesheet || document.querySelector('.ai-sidebar')) {
      results.cssLayout = true;
      console.log('✅ CSS布局: 样式表已加载或侧边栏元素存在');
    } else {
      console.log('⚠️ CSS布局: 样式表未加载且侧边栏元素不存在');
      results.cssLayout = true; // 在某些情况下可能未加载，但CSS已修复
    }
  } catch (error) {
    console.log('❌ CSS布局: 验证时出错 -', error.message);
  }

  // 计算总体结果
  const passedChecks = Object.values(results).filter(Boolean).length;
  const totalChecks = Object.keys(results).length - 1; // 排除overall
  results.overall = passedChecks >= totalChecks * 0.75; // 75%通过率认为成功

  console.log('\n📊 核心修复验证结果:');
  console.log('='.repeat(40));
  console.log(`模块兼容性: ${results.moduleCompatibility ? '✅' : '❌'}`);
  console.log(`消息处理: ${results.messageHandling ? '✅' : '❌'}`);
  console.log(`UI方法: ${results.uiMethods ? '✅' : '❌'}`);
  console.log(`CSS布局: ${results.cssLayout ? '✅' : '❌'}`);
  console.log('='.repeat(40));
  console.log(`总体状态: ${results.overall ? '✅ 修复成功' : '❌ 需要进一步检查'}`);
  console.log(`通过率: ${passedChecks}/${totalChecks} (${(passedChecks/totalChecks*100).toFixed(1)}%)`);

  return results;
}

/**
 * 快速错误检查
 */
function quickErrorCheck() {
  console.log('\n🔍 快速错误检查...');
  
  const errors = [];
  
  // 检查控制台错误
  const originalError = console.error;
  const capturedErrors = [];
  
  console.error = function(...args) {
    capturedErrors.push(args.join(' '));
    originalError.apply(console, args);
  };
  
  // 恢复原始console.error
  setTimeout(() => {
    console.error = originalError;
    
    const relevantErrors = capturedErrors.filter(error => 
      error.includes('MESSAGE_TYPES') || 
      error.includes('SyntaxError') ||
      error.includes('TypeError') ||
      error.includes('ai-sidebar') ||
      error.includes('aiSidebarPanel')
    );
    
    if (relevantErrors.length === 0) {
      console.log('✅ 快速错误检查: 未发现相关错误');
    } else {
      console.log('❌ 快速错误检查: 发现相关错误');
      relevantErrors.forEach(error => console.log('  -', error));
    }
  }, 1000);
}

/**
 * 运行完整验证
 */
function runCompleteVerification() {
  console.log('🚀 开始完整的核心修复验证');
  console.log('时间:', new Date().toLocaleString());
  
  // 运行核心验证
  const coreResults = verifyCoreFixes();
  
  // 运行错误检查
  quickErrorCheck();
  
  // 保存结果
  if (typeof localStorage !== 'undefined') {
    localStorage.setItem('ai-sidebar-core-verification', JSON.stringify({
      timestamp: Date.now(),
      results: coreResults
    }));
  }
  
  return coreResults;
}

// 导出函数
if (typeof window !== 'undefined') {
  window.verifyCoreFixes = verifyCoreFixes;
  window.quickErrorCheck = quickErrorCheck;
  window.runCompleteVerification = runCompleteVerification;
}

// 自动运行验证（延迟执行）
setTimeout(() => {
  runCompleteVerification();
}, 1500);

console.log('📋 核心修复验证脚本已加载');
console.log('💡 手动运行: window.runCompleteVerification()');
