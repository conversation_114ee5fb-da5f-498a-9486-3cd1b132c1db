/**
 * @file test-core-functionality.js
 * @description 核心功能验证测试脚本
 * @version 1.0.0
 */

/**
 * 核心功能测试类
 */
class CoreFunctionalityTester {
  constructor() {
    this.results = [];
    this.messageTypes = window.MESSAGE_TYPES || globalThis.MESSAGE_TYPES || {};
  }

  /**
   * 添加测试结果
   */
  addResult(testName, success, message, details = null) {
    this.results.push({
      testName,
      success,
      message,
      details,
      timestamp: Date.now()
    });
    
    const status = success ? '✅' : '❌';
    console.log(`${status} ${testName}: ${message}`);
    if (details) {
      console.log(`   详情:`, details);
    }
  }

  /**
   * 测试消息传递功能
   */
  async testMessagePassing() {
    console.log('\n📡 测试消息传递功能');
    
    // 测试1：基础消息发送
    if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
      try {
        const testMessage = {
          type: this.messageTypes.TEST_NOTIFICATION || 'ai:test:notification',
          payload: { test: true, timestamp: Date.now() },
          requestId: 'test-msg-' + Date.now()
        };

        const response = await new Promise((resolve, reject) => {
          const timeout = setTimeout(() => reject(new Error('超时')), 5000);
          
          chrome.runtime.sendMessage(testMessage, (response) => {
            clearTimeout(timeout);
            if (chrome.runtime.lastError) {
              reject(new Error(chrome.runtime.lastError.message));
            } else {
              resolve(response);
            }
          });
        });

        if (response && typeof response === 'object') {
          this.addResult(
            '消息传递-基础发送',
            true,
            '消息发送和接收正常',
            { hasResponse: true, responseKeys: Object.keys(response) }
          );
        } else {
          this.addResult(
            '消息传递-基础发送',
            false,
            '消息发送成功但响应异常',
            { response }
          );
        }
      } catch (error) {
        this.addResult(
          '消息传递-基础发送',
          false,
          '消息发送失败: ' + error.message
        );
      }
    } else {
      this.addResult(
        '消息传递-基础发送',
        false,
        'Chrome Runtime API不可用'
      );
    }

    // 测试2：状态查询消息
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      try {
        const statusMessage = {
          type: this.messageTypes.STATUS_GET || 'ai:status:get',
          payload: {},
          requestId: 'status-test-' + Date.now()
        };

        const response = await new Promise((resolve, reject) => {
          const timeout = setTimeout(() => reject(new Error('超时')), 3000);
          
          chrome.runtime.sendMessage(statusMessage, (response) => {
            clearTimeout(timeout);
            if (chrome.runtime.lastError) {
              reject(new Error(chrome.runtime.lastError.message));
            } else {
              resolve(response);
            }
          });
        });

        if (response && response.success) {
          this.addResult(
            '消息传递-状态查询',
            true,
            '状态查询消息处理正常',
            { statusData: response.data }
          );
        } else {
          this.addResult(
            '消息传递-状态查询',
            false,
            '状态查询失败',
            { response }
          );
        }
      } catch (error) {
        this.addResult(
          '消息传递-状态查询',
          false,
          '状态查询出错: ' + error.message
        );
      }
    }
  }

  /**
   * 测试UI交互功能
   */
  async testUIInteraction() {
    console.log('\n🎨 测试UI交互功能');
    
    // 测试1：侧边栏面板实例
    if (window.aiSidebarPanel) {
      this.addResult(
        'UI交互-面板实例',
        true,
        '侧边栏面板实例存在'
      );

      // 测试2：关键方法调用
      try {
        const panel = window.aiSidebarPanel;
        
        // 测试getAnalysisOptions方法
        if (typeof panel.getAnalysisOptions === 'function') {
          const options = panel.getAnalysisOptions();
          this.addResult(
            'UI交互-分析选项获取',
            true,
            '分析选项获取方法正常',
            { options }
          );
        } else {
          this.addResult(
            'UI交互-分析选项获取',
            false,
            'getAnalysisOptions方法不存在'
          );
        }

        // 测试getCurrentPageInfo方法
        if (typeof panel.getCurrentPageInfo === 'function') {
          try {
            const pageInfo = await panel.getCurrentPageInfo();
            this.addResult(
              'UI交互-页面信息获取',
              true,
              '页面信息获取方法正常',
              { hasTitle: !!pageInfo.title, hasUrl: !!pageInfo.url }
            );
          } catch (error) {
            this.addResult(
              'UI交互-页面信息获取',
              false,
              '页面信息获取失败: ' + error.message
            );
          }
        } else {
          this.addResult(
            'UI交互-页面信息获取',
            false,
            'getCurrentPageInfo方法不存在'
          );
        }

      } catch (error) {
        this.addResult(
          'UI交互-方法调用',
          false,
          '方法调用测试出错: ' + error.message
        );
      }
    } else {
      this.addResult(
        'UI交互-面板实例',
        false,
        '侧边栏面板实例不存在'
      );
    }

    // 测试3：DOM元素存在性
    const keyElements = [
      '.ai-sidebar',
      '.ai-sidebar__main',
      '.ai-sidebar__panel'
    ];

    let foundElements = 0;
    keyElements.forEach(selector => {
      if (document.querySelector(selector)) {
        foundElements++;
      }
    });

    if (foundElements > 0) {
      this.addResult(
        'UI交互-DOM元素',
        true,
        `关键DOM元素存在 (${foundElements}/${keyElements.length})`,
        { foundElements, totalElements: keyElements.length }
      );
    } else {
      this.addResult(
        'UI交互-DOM元素',
        false,
        '关键DOM元素不存在'
      );
    }
  }

  /**
   * 测试内容捕获功能
   */
  async testContentCapture() {
    console.log('\n📄 测试内容捕获功能');
    
    // 测试1：MESSAGE_TYPES可用性
    if (this.messageTypes.CONTENT_CAPTURED) {
      this.addResult(
        '内容捕获-消息类型',
        true,
        'CONTENT_CAPTURED消息类型可用'
      );
    } else {
      this.addResult(
        '内容捕获-消息类型',
        false,
        'CONTENT_CAPTURED消息类型不可用'
      );
    }

    // 测试2：内容捕获消息发送
    if (typeof chrome !== 'undefined' && chrome.runtime && this.messageTypes.CONTENT_CAPTURED) {
      try {
        const captureMessage = {
          type: this.messageTypes.CONTENT_CAPTURED,
          payload: {
            url: window.location.href,
            title: document.title,
            textContent: document.body.innerText.substring(0, 1000),
            timestamp: Date.now()
          },
          requestId: 'capture-test-' + Date.now()
        };

        const response = await new Promise((resolve, reject) => {
          const timeout = setTimeout(() => reject(new Error('超时')), 3000);
          
          chrome.runtime.sendMessage(captureMessage, (response) => {
            clearTimeout(timeout);
            if (chrome.runtime.lastError) {
              reject(new Error(chrome.runtime.lastError.message));
            } else {
              resolve(response);
            }
          });
        });

        if (response) {
          this.addResult(
            '内容捕获-消息发送',
            true,
            '内容捕获消息发送成功',
            { responseReceived: true }
          );
        } else {
          this.addResult(
            '内容捕获-消息发送',
            false,
            '内容捕获消息无响应'
          );
        }
      } catch (error) {
        this.addResult(
          '内容捕获-消息发送',
          false,
          '内容捕获消息发送失败: ' + error.message
        );
      }
    } else {
      this.addResult(
        '内容捕获-消息发送',
        false,
        '无法测试内容捕获消息发送'
      );
    }
  }

  /**
   * 生成测试报告
   */
  generateReport() {
    const total = this.results.length;
    const passed = this.results.filter(r => r.success).length;
    const failed = total - passed;
    const successRate = total > 0 ? (passed / total * 100).toFixed(1) : '0';

    console.log('\n📊 核心功能测试报告');
    console.log('='.repeat(50));
    console.log(`总测试数: ${total}`);
    console.log(`通过: ${passed}`);
    console.log(`失败: ${failed}`);
    console.log(`成功率: ${successRate}%`);
    console.log('='.repeat(50));

    // 按类别统计
    const categories = {};
    this.results.forEach(result => {
      const category = result.testName.split('-')[0];
      if (!categories[category]) {
        categories[category] = { total: 0, passed: 0 };
      }
      categories[category].total++;
      if (result.success) {
        categories[category].passed++;
      }
    });

    console.log('\n📈 分类统计:');
    Object.entries(categories).forEach(([category, stats]) => {
      const rate = (stats.passed / stats.total * 100).toFixed(1);
      console.log(`${category}: ${stats.passed}/${stats.total} (${rate}%)`);
    });

    return {
      summary: {
        total,
        passed,
        failed,
        successRate: successRate + '%'
      },
      categories,
      results: this.results
    };
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 开始核心功能验证测试');
    console.log('时间:', new Date().toLocaleString());

    try {
      await this.testMessagePassing();
      await this.testUIInteraction();
      await this.testContentCapture();

      const report = this.generateReport();
      
      // 保存结果
      if (typeof localStorage !== 'undefined') {
        localStorage.setItem('ai-sidebar-functionality-test', JSON.stringify(report));
        console.log('\n💾 测试结果已保存到localStorage');
      }

      return report;
    } catch (error) {
      console.error('❌ 测试执行出错:', error);
      this.addResult(
        '系统-测试执行',
        false,
        '测试执行出错: ' + error.message
      );
      return this.generateReport();
    }
  }
}

/**
 * 运行核心功能测试
 */
async function runCoreFunctionalityTest() {
  const tester = new CoreFunctionalityTester();
  return await tester.runAllTests();
}

// 导出函数
if (typeof window !== 'undefined') {
  window.runCoreFunctionalityTest = runCoreFunctionalityTest;
  window.CoreFunctionalityTester = CoreFunctionalityTester;
}

// 自动运行测试
setTimeout(() => {
  runCoreFunctionalityTest().then(report => {
    console.log('🎉 核心功能测试完成');
  }).catch(error => {
    console.error('❌ 核心功能测试失败:', error);
  });
}, 2500);

console.log('📋 核心功能测试脚本已加载');
console.log('💡 手动运行: window.runCoreFunctionalityTest()');
